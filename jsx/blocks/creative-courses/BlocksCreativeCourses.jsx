import { useMemo } from "preact/hooks";
import { Pagination } from "./Pagination";
import { CoursesSidebar } from "./CoursesSidebar";
import { small, useBreakpoint } from "../../hooks";
import { queryStringURL } from "../../utils";

const subjectToCategory = {
  business: "Business Courses",
  "data-analytics": "Data analytics Courses",
  design: "Design Courses",
  "digital-and-it": "Digital & IT Courses",
  finance: "Finance Courses",
  hr: "HR Courses",
  leadership: "Leadership Courses",
  management: "Management Courses",
  marketing: "Marketing Courses",
};

const deliveryToCategory = {
  "on-demand": "On Demand",
  "on-campus": "On Campus",
  "full-time": "Full-time",
  "part-time-morning": "morning",
  "part-time-evening": "evening",
};

export function BlocksCreativeCourses({ featured: initialFeatured, "data-block-id": blockId }) {
  const breakpoint = useBreakpoint();
  const perPage = breakpoint <= small ? 16 : 15;

  const urlParams = new URLSearchParams(window.location.search);
  const keywords = urlParams.get("keywords") || "";
  const page = parseInt(urlParams.get("page") || "1", 10);
  const sort = urlParams.get("sort");
  const subject = urlParams.get("subject");
  const delivery = urlParams.get("delivery");

  // Ideally, 'courses' should be passed as a prop instead of being read from window
  // @ts-expect-error - courses is expected to be globally available for now
  const coursesFromWindow = window.courses || [];

  // Parse the featured data safely - try script tag first, then fallback to prop
  const featuredArray = useMemo(() => {
    // Try to get data from script tag first (new method)
    if (blockId) {
      try {
        const scriptElement = document.getElementById(`${blockId}-data`);
        if (scriptElement) {
          const scriptContent = scriptElement.textContent || scriptElement.innerHTML;
          if (scriptContent.trim()) {
            const parsed = JSON.parse(scriptContent);
            return Array.isArray(parsed) ? parsed : [];
          }
        }
      } catch (e) {
        console.error("Failed to parse featured data from script tag:", {
          blockId,
          error: e.message,
        });
      }
    }

    // Fallback to prop method (legacy support)
    if (typeof initialFeatured === "string") {
      // Handle empty or whitespace-only strings
      if (!initialFeatured.trim()) {
        console.warn("Featured prop is empty string, using empty array");
        return [];
      }
      try {
        const parsed = JSON.parse(initialFeatured);
        return Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        console.error("Failed to parse 'featured' prop string:", {
          value: initialFeatured,
          length: initialFeatured.length,
          error: e.message,
        });
        return [];
      }
    }
    return Array.isArray(initialFeatured) ? initialFeatured : [];
  }, [initialFeatured, blockId]);

  const results = useMemo(() => {
    let allCourses = [...coursesFromWindow];

    if (!sort || sort === "most-popular") {
      // Default sort for "most-popular" or no sort
      const trendingUuids = featuredArray || [];
      const trending = trendingUuids.map((uuid) => allCourses.find((c) => c.uuid === uuid)).filter(Boolean);
      const nonTrending = allCourses.filter((course) => !trendingUuids.includes(course.uuid));
      allCourses = [...trending, ...nonTrending];
    }

    if (subject && subjectToCategory[subject]) {
      allCourses = allCourses.filter((course) => course.subjects?.includes(subjectToCategory[subject]));
    }

    if (delivery && deliveryToCategory[delivery]) {
      allCourses = allCourses.filter((course) => course.deliveries?.includes(deliveryToCategory[delivery]));
    }

    const filteredCourses = allCourses.filter((course) => {
      if (!keywords || keywords.length < 2) return true;
      const courseTitle = course.title?.toLowerCase().replace(/\s+/g, "") || "";
      const searchKeywords = keywords.toLowerCase().replace(/\s+/g, "");
      const searchTerms = course.search_terms || [];

      return courseTitle.includes(searchKeywords) || searchTerms.some((term) => term.toLowerCase().replace(/\s+/g, "").includes(searchKeywords));
    });

    if (sort === "name") {
      filteredCourses.sort((a, b) => a.title.localeCompare(b.title));
    }
    // If sort is "most-popular", it's already handled by the initial ordering.

    return filteredCourses;
  }, [keywords, sort, subject, delivery, coursesFromWindow, featuredArray]);

  const setSorting = (value) => {
    window.location.href = queryStringURL("sort", value === "most-popular" ? undefined : value);
  };

  // flash_original_url was present in the original code snippet for clearing keywords.
  // Creating a similar clear URL for keywords:
  const clearKeywordsURL = queryStringURL("keywords", undefined);

  return (
    <div className='creative_courses__grid'>
      <CoursesSidebar />
      <div>
        {results.length > 0 ? (
          <>
            <div className='creative_courses__showing'>
              <p>
                {page * perPage < results.length ? (
                  <>
                    <strong>
                      Showing {(page - 1) * perPage + 1}-{Math.min(page * perPage, results.length)}
                    </strong>{" "}
                    of {results.length} courses
                  </>
                ) : (
                  <>
                    <strong>Showing {results.length}</strong> of {results.length} courses
                  </>
                )}
                {keywords.length > 1 && (
                  <>
                    {" "}
                    for "{keywords}" <a href={clearKeywordsURL}>clear search</a>
                  </>
                )}
              </p>
              <div className='creative_courses__sort'>
                <select onChange={(e) => setSorting(e.target.value)} value={sort || "most-popular"}>
                  <option value='most-popular'>Sort by Most Popular</option>
                  <option value='name'>Sort by Name (A-Z)</option>
                </select>
                <svg width='7' height='4' viewBox='0 0 7 4' fill='none' xmlns='http://www.w3.org/2000/svg'>
                  <path d='M6.22177 0.0226746L3.70002 2.468L1.17828 0.0226746L0.400024 0.777325L3.70002 3.97732L7.00002 0.777325L6.22177 0.0226746Z' fill='#102A43' fillOpacity='0.7' />
                </svg>
              </div>
            </div>
            <Pagination perPage={perPage} className='creative_courses__courses'>
              {results.map((course) => (
                <Course key={course.uuid} {...course} />
              ))}
            </Pagination>
          </>
        ) : (
          <div className='creative_courses__no_results'>
            <p>
              Sorry, we couldn't find any course matching your criteria. Please try again with another search.
              {keywords.length > 1 && (
                <>
                  <br />
                  <br />
                  <a href={clearKeywordsURL}>Clear search for "{keywords}"</a>
                </>
              )}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

const Course = ({ url, image, tag, title }) => (
  <a href={url} className='creative_courses__course'>
    <div className='creative_courses__image' style={{ backgroundImage: `url("${image || "https://placehold.co/290x140"}")` }}>
      {tag && <span className='creative_courses__badge'>{tag}</span>}
    </div>
    <div className='creative_courses__content'>
      <div data-follow-height='creative-courses-listing'>
        <h3>{title}</h3>
      </div>
      <span className='creative_courses__find_out_more'>Learn More</span>
    </div>
  </a>
);
