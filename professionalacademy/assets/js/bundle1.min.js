
/* File: assets/js/flash/1.5.2/plugins/jparam.js */
/*
 jquery-param (c) 2015 KNOWLEDGECODE | MIT
*/
(function(h){var c=function(c){var f=[],g=function(d,a){a="function"===typeof a?a():a;a=null===a?"":void 0===a?"":a;f[f.length]=encodeURIComponent(d)+"="+encodeURIComponent(a)},e=function(d,a){var c;if(d)if(Array.isArray(a)){var b=0;for(c=a.length;b<c;b++)e(d+"["+("object"===typeof a[b]&&a[b]?b:"")+"]",a[b])}else if("[object Object]"===String(a))for(b in a)e(d+"["+b+"]",a[b]);else g(d,a);else if(Array.isArray(a))for(b=0,c=a.length;b<c;b++)g(a[b].name,a[b].value);else for(b in a)e(b,a[b]);return f};
return e("",c).join("&")};"object"===typeof module&&"object"===typeof module.exports?module.exports=c:"function"===typeof define&&define.amd?define([],function(){return c}):h.param=c})(this);


/* File: assets/js/flash/1.5.2/plugins/lazysizes.min.js */
/*! lazysizes - v5.1.1 */
!function(a,b){var c=b(a,a.document);a.lazySizes=c,"object"==typeof module&&module.exports&&(module.exports=c)}("undefined"!=typeof window?window:{},function(a,b){"use strict";var c,d;if(function(){var b,c={lazyClass:"lazyload",loadedClass:"lazyloaded",loadingClass:"lazyloading",preloadClass:"lazypreload",errorClass:"lazyerror",autosizesClass:"lazyautosizes",srcAttr:"data-src",srcsetAttr:"data-srcset",sizesAttr:"data-sizes",minSize:40,customMedia:{},init:!0,expFactor:1.5,hFac:.8,loadMode:2,loadHidden:!0,ricTimeout:0,throttleDelay:125};d=a.lazySizesConfig||a.lazysizesConfig||{};for(b in c)b in d||(d[b]=c[b])}(),!b||!b.getElementsByClassName)return{init:function(){},cfg:d,noSupport:!0};var e=b.documentElement,f=a.Date,g=a.HTMLPictureElement,h="addEventListener",i="getAttribute",j=a[h],k=a.setTimeout,l=a.requestAnimationFrame||k,m=a.requestIdleCallback,n=/^picture$/i,o=["load","error","lazyincluded","_lazyloaded"],p={},q=Array.prototype.forEach,r=function(a,b){return p[b]||(p[b]=new RegExp("(\\s|^)"+b+"(\\s|$)")),p[b].test(a[i]("class")||"")&&p[b]},s=function(a,b){r(a,b)||a.setAttribute("class",(a[i]("class")||"").trim()+" "+b)},t=function(a,b){var c;(c=r(a,b))&&a.setAttribute("class",(a[i]("class")||"").replace(c," "))},u=function(a,b,c){var d=c?h:"removeEventListener";c&&u(a,b),o.forEach(function(c){a[d](c,b)})},v=function(a,d,e,f,g){var h=b.createEvent("Event");return e||(e={}),e.instance=c,h.initEvent(d,!f,!g),h.detail=e,a.dispatchEvent(h),h},w=function(b,c){var e;!g&&(e=a.picturefill||d.pf)?(c&&c.src&&!b[i]("srcset")&&b.setAttribute("srcset",c.src),e({reevaluate:!0,elements:[b]})):c&&c.src&&(b.src=c.src)},x=function(a,b){return(getComputedStyle(a,null)||{})[b]},y=function(a,b,c){for(c=c||a.offsetWidth;c<d.minSize&&b&&!a._lazysizesWidth;)c=b.offsetWidth,b=b.parentNode;return c},z=function(){var a,c,d=[],e=[],f=d,g=function(){var b=f;for(f=d.length?e:d,a=!0,c=!1;b.length;)b.shift()();a=!1},h=function(d,e){a&&!e?d.apply(this,arguments):(f.push(d),c||(c=!0,(b.hidden?k:l)(g)))};return h._lsFlush=g,h}(),A=function(a,b){return b?function(){z(a)}:function(){var b=this,c=arguments;z(function(){a.apply(b,c)})}},B=function(a){var b,c=0,e=d.throttleDelay,g=d.ricTimeout,h=function(){b=!1,c=f.now(),a()},i=m&&g>49?function(){m(h,{timeout:g}),g!==d.ricTimeout&&(g=d.ricTimeout)}:A(function(){k(h)},!0);return function(a){var d;(a=!0===a)&&(g=33),b||(b=!0,d=e-(f.now()-c),d<0&&(d=0),a||d<9?i():k(i,d))}},C=function(a){var b,c,d=99,e=function(){b=null,a()},g=function(){var a=f.now()-c;a<d?k(g,d-a):(m||e)(e)};return function(){c=f.now(),b||(b=k(g,d))}},D=function(){var g,l,m,o,p,y,D,F,G,H,I,J,K=/^img$/i,L=/^iframe$/i,M="onscroll"in a&&!/(gle|ing)bot/.test(navigator.userAgent),N=0,O=0,P=0,Q=-1,R=function(a){P--,(!a||P<0||!a.target)&&(P=0)},S=function(a){return null==J&&(J="hidden"==x(b.body,"visibility")),J||"hidden"!=x(a.parentNode,"visibility")&&"hidden"!=x(a,"visibility")},T=function(a,c){var d,f=a,g=S(a);for(F-=c,I+=c,G-=c,H+=c;g&&(f=f.offsetParent)&&f!=b.body&&f!=e;)(g=(x(f,"opacity")||1)>0)&&"visible"!=x(f,"overflow")&&(d=f.getBoundingClientRect(),g=H>d.left&&G<d.right&&I>d.top-1&&F<d.bottom+1);return g},U=function(){var a,f,h,j,k,m,n,p,q,r,s,t,u=c.elements;if((o=d.loadMode)&&P<8&&(a=u.length)){for(f=0,Q++;f<a;f++)if(u[f]&&!u[f]._lazyRace)if(!M||c.prematureUnveil&&c.prematureUnveil(u[f]))aa(u[f]);else if((p=u[f][i]("data-expand"))&&(m=1*p)||(m=O),r||(r=!d.expand||d.expand<1?e.clientHeight>500&&e.clientWidth>500?500:370:d.expand,c._defEx=r,s=r*d.expFactor,t=d.hFac,J=null,O<s&&P<1&&Q>2&&o>2&&!b.hidden?(O=s,Q=0):O=o>1&&Q>1&&P<6?r:N),q!==m&&(y=innerWidth+m*t,D=innerHeight+m,n=-1*m,q=m),h=u[f].getBoundingClientRect(),(I=h.bottom)>=n&&(F=h.top)<=D&&(H=h.right)>=n*t&&(G=h.left)<=y&&(I||H||G||F)&&(d.loadHidden||S(u[f]))&&(l&&P<3&&!p&&(o<3||Q<4)||T(u[f],m))){if(aa(u[f]),k=!0,P>9)break}else!k&&l&&!j&&P<4&&Q<4&&o>2&&(g[0]||d.preloadAfterLoad)&&(g[0]||!p&&(I||H||G||F||"auto"!=u[f][i](d.sizesAttr)))&&(j=g[0]||u[f]);j&&!k&&aa(j)}},V=B(U),W=function(a){var b=a.target;if(b._lazyCache)return void delete b._lazyCache;R(a),s(b,d.loadedClass),t(b,d.loadingClass),u(b,Y),v(b,"lazyloaded")},X=A(W),Y=function(a){X({target:a.target})},Z=function(a,b){try{a.contentWindow.location.replace(b)}catch(c){a.src=b}},$=function(a){var b,c=a[i](d.srcsetAttr);(b=d.customMedia[a[i]("data-media")||a[i]("media")])&&a.setAttribute("media",b),c&&a.setAttribute("srcset",c)},_=A(function(a,b,c,e,f){var g,h,j,l,o,p;(o=v(a,"lazybeforeunveil",b)).defaultPrevented||(e&&(c?s(a,d.autosizesClass):a.setAttribute("sizes",e)),h=a[i](d.srcsetAttr),g=a[i](d.srcAttr),f&&(j=a.parentNode,l=j&&n.test(j.nodeName||"")),p=b.firesLoad||"src"in a&&(h||g||l),o={target:a},s(a,d.loadingClass),p&&(clearTimeout(m),m=k(R,2500),u(a,Y,!0)),l&&q.call(j.getElementsByTagName("source"),$),h?a.setAttribute("srcset",h):g&&!l&&(L.test(a.nodeName)?Z(a,g):a.src=g),f&&(h||l)&&w(a,{src:g})),a._lazyRace&&delete a._lazyRace,t(a,d.lazyClass),z(function(){var b=a.complete&&a.naturalWidth>1;p&&!b||(b&&s(a,"ls-is-cached"),W(o),a._lazyCache=!0,k(function(){"_lazyCache"in a&&delete a._lazyCache},9)),"lazy"==a.loading&&P--},!0)}),aa=function(a){if(!a._lazyRace){var b,c=K.test(a.nodeName),e=c&&(a[i](d.sizesAttr)||a[i]("sizes")),f="auto"==e;(!f&&l||!c||!a[i]("src")&&!a.srcset||a.complete||r(a,d.errorClass)||!r(a,d.lazyClass))&&(b=v(a,"lazyunveilread").detail,f&&E.updateElem(a,!0,a.offsetWidth),a._lazyRace=!0,P++,_(a,b,f,e,c))}},ba=C(function(){d.loadMode=3,V()}),ca=function(){3==d.loadMode&&(d.loadMode=2),ba()},da=function(){if(!l){if(f.now()-p<999)return void k(da,999);l=!0,d.loadMode=3,V(),j("scroll",ca,!0)}};return{_:function(){p=f.now(),c.elements=b.getElementsByClassName(d.lazyClass),g=b.getElementsByClassName(d.lazyClass+" "+d.preloadClass),j("scroll",V,!0),j("resize",V,!0),a.MutationObserver?new MutationObserver(V).observe(e,{childList:!0,subtree:!0,attributes:!0}):(e[h]("DOMNodeInserted",V,!0),e[h]("DOMAttrModified",V,!0),setInterval(V,999)),j("hashchange",V,!0),["focus","mouseover","click","load","transitionend","animationend"].forEach(function(a){b[h](a,V,!0)}),/d$|^c/.test(b.readyState)?da():(j("load",da),b[h]("DOMContentLoaded",V),k(da,2e4)),c.elements.length?(U(),z._lsFlush()):V()},checkElems:V,unveil:aa,_aLSL:ca}}(),E=function(){var a,c=A(function(a,b,c,d){var e,f,g;if(a._lazysizesWidth=d,d+="px",a.setAttribute("sizes",d),n.test(b.nodeName||""))for(e=b.getElementsByTagName("source"),f=0,g=e.length;f<g;f++)e[f].setAttribute("sizes",d);c.detail.dataAttr||w(a,c.detail)}),e=function(a,b,d){var e,f=a.parentNode;f&&(d=y(a,f,d),e=v(a,"lazybeforesizes",{width:d,dataAttr:!!b}),e.defaultPrevented||(d=e.detail.width)&&d!==a._lazysizesWidth&&c(a,f,e,d))},f=function(){var b,c=a.length;if(c)for(b=0;b<c;b++)e(a[b])},g=C(f);return{_:function(){a=b.getElementsByClassName(d.autosizesClass),j("resize",g)},checkElems:g,updateElem:e}}(),F=function(){!F.i&&b.getElementsByClassName&&(F.i=!0,E._(),D._())};return k(function(){d.init&&F()}),c={cfg:d,autoSizer:E,loader:D,init:F,uP:w,aC:s,rC:t,hC:r,fire:v,gW:y,rAF:z}});

/* File: assets/js/flash/1.5.2/plugins/lightbox.js */
"use strict";

function _typeof(obj) { if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }

(function (root, factory) {
  if (typeof define === 'function' && define.amd) {
    define([], function () {
      return factory(root);
    });
  } else if ((typeof exports === "undefined" ? "undefined" : _typeof(exports)) === 'object') {
    module.exports = factory(root);
  } else {
    root.lightbox = factory(root);
  }
})(typeof global !== 'undefined' ? global : typeof window !== 'undefined' ? window : void 0, function (window) {
  'use strict';
  /**
   * DEFAULTS AND LIGHTBOX OBJECT
   * =============================================
   */

  var defaults = {
    clone_content: true,
    // If false will move the actual node in & out of the lightbox
    custom_class: '',
    // Optional classes for the lightbox
    detect_content_type: true,
    trigger_attribute: 'data-lightbox',
    trigger_event: 'click',
    group_selector: '[data-lightbox-group]',
    arrows_container_class: 'lightbox__arrows',
    next_arrow: '<div class="lightbox__arrow lightbox__arrow--next"></div>',
    prev_arrow: '<div class="lightbox__arrow lightbox__arrow--prev"></div>' // Storing the position of a node with a placeholder in case clone_content is false

  };
  var placeholder_node = null; // Placeholder node to put back content when clone_content is false

  var lightbox_node = null; // The node of the lightbox

  var content_node = null; // The node of the content of the lightbox

  var current_trigger = null; // The trigger clicked to open the lightbox
  // State variables

  var closing_lightbox = false;
  /**
   * PRIVATE METHODS
   * =============================================
   */

  /**
   * Get the video source and the video id for the embed 
   * @param  {String} video_url The url of the video
   * @return {{id: {String}, source: {String}}|Bool}  Return the object with the video id and the video source
   * or false if it's not recognised as a video
   */

  function getVideoData(video_url) {
    var video = {
      id: '',
      source: ''
    }; // Getting the source

    if (video_url.indexOf('yout') >= 0) {
      video.source = 'youtube';
    }

    if (video_url.indexOf('vimeo') >= 0) {
      video.source = 'vimeo';
    } // Get youtube video ID


    if (video.source == "youtube") {
      var youtube_regex = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;

      var _match = video_url.match(youtube_regex);

      video.id = _match && _match[2] ? _match[2] : '';
    } // Get Vimeo video ID


    if (video.source == "vimeo") {
      if (video_url.indexOf("clip_id") > -1) {
        var _vimeo_regex = /(clip_id=(\d+))/;

        var _match2 = video_url.match(_vimeo_regex);
      } else if (video_url.indexOf("player") > -1) {
        var _vimeo_regex2 = /player\.vimeo\.com\/video\/([0-9]*)/;

        var _match3 = video_url.match(_vimeo_regex2);

        _match3[2] = _match3[1];
      } else {
        var vimeo_regex = /(?:http|https):\/\/(www\.)?vimeo.com\/(\d+)($|\/)/;
        var match = video_url.match(vimeo_regex);
      }

      video.id = match && match[2] ? match[2] : '';
    }

    if (video.id && video.source) {
      return video;
    } else {
      return false;
    }
  }
  /**
   * Get the html associated to content for the lightbox. 
   * @param  {String} content         The conten for the lightbox
   * @param  {Bool}   detect_content_type   If true it will filter the output of the
   * content based on the content type (image url, video url, url, css selector), if false it will
   * return a plain string
   * @return {String|Node}   The actual content for the lightbox
   */


  var getHtmlFromContent = function getHtmlFromContent(content, options) {
    var content_output = {
      type: null,
      filtered_content: null,
      content: content
    }; // The output of this function
    // Check if content is undefined

    if (typeof content === 'undefined') {
      console.warn('Content is undefined');
      return;
    } // Get the content as plain text or try to detect it automatically


    if (!options.detect_content_type && typeof content === 'string') {
      // Plain text or html
      content_output.type = 'string';
      content_output.filtered_content = content;
    } else {
      // Check if the content is an image
      if (content.indexOf('http://') == 0 || content.indexOf('https://') == 0) {
        // IMAGE - Check if it's an image
        // Getting the extension
        var extension = content.split('?')[0].split('.').pop(); // List of images extensions

        var images_extensions = ['jpg', 'png', 'svg'];

        if (images_extensions.indexOf(extension) > -1) {
          content_output.filtered_content = "<img src=\"".concat(content, "\" alt=\"{alt}\" class=\"lightbox__image\" />");
          content_output.type = 'image';
        } // VIDEO - Check if it's a youtube video


        if (!content_output.type) {
          var video_data = getVideoData(content);

          if (video_data) {
            // YouTube
            if (video_data.source === 'youtube') {
              content_output.filtered_content = "<div class=\"lightbox__video\">\n                          <iframe   data-cookiescript=\"accepted\" data-cookiecategory=\"functionality\" alt=\"Please accept cookie policy first\"  data-src=\"https://www.youtube.com/embed/".concat(video_data.id, "?autoplay=1&showinfo=0\" frameborder=\"0\" webkitallowfullscreen mozallowfullscreen allowfullscreen allow=\"autoplay\" class=\"lightbox__video_iframe\"></iframe>\n                        </div>");
              content_output.type = 'video';
            } // Vimeo


            if (video_data.source === 'vimeo') {
              content_output.filtered_content = "<div class=\"lightbox__video\">\n                          <iframe   data-cookiescript=\"accepted\" data-cookiecategory=\"functionality\" alt=\"Please accept cookie policy first\"  data-src=\"//player.vimeo.com/video/".concat(video_data.id, "?title=0&amp;byline=0&amp;portrait=0&amp;color=96c159&amp;api=1&amp;autoplay=1&amp;player_id=video_").concat(video_data.id, "\" frameborder=\"0\" webkitallowfullscreen mozallowfullscreen allowfullscreen  allow=\"autoplay\" class=\"lightbox__video_iframe\"></iframe>\n                        </div>");
              content_output.type = 'video';
            }
          }
        } // EXTERNAL PAGE


        if (!content_output.type) {
          content_output.type = 'url';
          content_output.filtered_content = "<div class=\"lightbox__url\">\n                          <iframe src=\"".concat(content, "\" frameborder=\"0\" class=\"lightbox__url_iframe\"></iframe>\n                        </div>");
        }
      }

      try {
        // NODE - Check if it's an html node
        if (!content_output.type && document.querySelector(content)) {
          content_output.type = 'node'; // Clone the node if clone_content is true or use the original node
          // if false

          if (options.clone_content) {
            content_output.filtered_content = document.querySelector(content).cloneNode(true);
          } else {
            content_node = document.querySelector(content); // Preparing the placeholder node

            placeholder_node = document.createElement('div');
            placeholder_node.classList.add("lightbox_content_placeholder");
            placeholder_node.style.display = 'none';
            content_node.parentNode.insertBefore(placeholder_node, content_node);
            content_output.filtered_content = content_node;
          }
        }
      } catch (e) {} // STRING - Fallback to text if no other type was detected


      if (!content_output.type) {
        content_output.type = 'string';
        content_output.filtered_content = content;
      }
    }

    return new Promise(function (resolve, reject) {
      resolve(content_output);
    });
  };
  /**
   * Get the the content from a lightbox trigger
   * @param  {Node}   trigger       The node of the trigger
   * @param  {String} trigger_attribute   The html attribute from which retrieve the content
   * @return {String} The content
   */


  var getContentFromTrigger = function getContentFromTrigger(trigger, trigger_attribute) {
    var content = {
      data: '',
      "class": ''
    };
    current_trigger = trigger;

    if (!trigger) {
      console.warn('No trigger is defined');
      return;
    } // Check if the trigger has the right
    // attribute set to retrieve the content


    var has_trigger_lightbox_attribute = trigger.hasAttribute(trigger_attribute);

    if (has_trigger_lightbox_attribute) {
      // Using the attribute set in the options
      content.data = trigger.getAttribute(trigger_attribute);
    } else if (trigger.matches('a[href]')) {
      // If it's an anchor without the trigger attribute set, get the href value
      content.data = trigger.getAttribute('href');
    } else {
      console.warn('The trigger has not any attribute set for content.');
      return;
    } // Getting optional classes


    if (trigger.hasAttribute('data-lightbox-class')) {
      content["class"] = trigger.getAttribute('data-lightbox-class');
    }

    return content;
  };
  /**
   * Prepare the lightbox and append it to the <body>
   * @return {Node} The node of the lightbox
   */


  var prepareLightbox = function prepareLightbox() {
    // Init Lightbox Div
    if (!lightbox_node) {
      lightbox_node = document.createElement('div');
      lightbox_node.classList.add("lightbox");
      lightbox_node.style.opacity = 0;
      document.body.appendChild(lightbox_node);
    }
  };
  /**
   * Add the arrows to the lightbox
   * @return {void}
   */


  var addArrows = function addArrows(options) {
    // Init empty nodes
    var next_node = null;
    var prev_node = null; // If the group option is not set and the current trigger has the group_selector set instead
    // use the group mode anyway

    if (!options.group && current_trigger && current_trigger.matches(options.group_selector)) {
      options.group = true;
    } // If it's a group and a group selector option is set


    if (options.group && options.group_selector) {
      var group_nodes = document.querySelectorAll(options.group_selector); // Add the arrows just if the group has more than 1 item

      if (group_nodes.length > 1) {
        group_nodes.forEach(function (node, index) {
          if (node.isSameNode(current_trigger)) {
            // Getting the previous node
            if (index > 0) {
              prev_node = group_nodes[index - 1];
            } else {
              prev_node = group_nodes[group_nodes.length - 1];
            } // Getting the next node


            if (index < group_nodes.length - 1) {
              next_node = group_nodes[index + 1];
            } else {
              next_node = group_nodes[0];
            }
          }
        });
      }
    } // Appending the arrows container


    var lightbox_inner = lightbox_node.querySelector('.lightbox__content_inner');


    if (next_node || prev_node) {
      var arrows_container = document.createElement('div');
      arrows_container.classList.add(options.arrows_container_class);
      lightbox_inner.insertAdjacentElement('beforeend', arrows_container);
    } // Appending the arrows


    if (prev_node) {
      arrows_container.insertAdjacentHTML('beforeend', options.prev_arrow);
      arrows_container.lastChild.addEventListener('click', function (event) {
        prev_node.click();
      });
    }

    if (next_node) {
      arrows_container.insertAdjacentHTML('beforeend', options.next_arrow);
      arrows_container.lastChild.addEventListener('click', function (event) {
        next_node.click();
      });
    }
  };
  /**
   * LIGHTBOX CONSTRUCTOR
   * =============================================
   * 
   * The actual Lightbox constructor object with public API
   * @param  {String} content   The content for the lightbox
   * @param  {Object} options   The options for the lightbox
   * @return {String} The content
   */


  var lightbox = function lightbox(content, options) {
    // Object where to store public methods
    var public_methods = {};
    
    /**
     * Prepare the lightbox and append it to the <body>
     * @param {Object} The lightbox object
     * @param {String} The html content to append into the ligthbox
     * @return {Bool} Success or not
     */

    public_methods.openLightbox = function (content) {
      prepareLightbox();
      options.custom_class += " ".concat(content["class"]); // Setup the lightbox close buttone

      lightbox_node.addEventListener('click', function (event) {
        if (!closing_lightbox && (event.target.matches('.lightbox') || event.target.matches('.lightbox__close') || event.target.matches('.lightbox__close_trigger'))) {
          public_methods.closeLightbox();
        }
      });
      public_methods.setContent(content.data).then(function () {
        var flashReadyEvent = new CustomEvent('lightboxVisible');
        document.body.dispatchEvent(flashReadyEvent);
        lightbox_node.style.opacity = 1;
        return true;
      })["catch"](function (error) {
        console.error("Something went really wrong trying to open the lightbox: ".concat(error));
        return false;
      });
    };
    /**
     * Closes the ligthbox and destroys it
     */


    public_methods.closeLightbox = function () {
      // If the content node was moved from its original location
      // put it back
      if (!options.clone_content && content_node) {
        placeholder_node.parentNode.insertBefore(content_node, placeholder_node);
        placeholder_node.remove();
      } // Removing the lightbox


      lightbox_node.remove(); // Resetting nodes

      lightbox_node = null;
      content_node = null;
      placeholder_node = null;
      closing_lightbox = false;
      current_trigger = null;

      // Event
      var flashClosedEvent = new CustomEvent('lightboxClosed');
      document.body.dispatchEvent(flashClosedEvent);
    };
    /**
     * Change the content in the lightbox
     * @param {String} content The content for the lightbox
     */


    public_methods.setContent = function (content) {
      return new Promise(function (resolve, reject) {
        getHtmlFromContent(content, options) // Get the HTML associated with the current content
        .then(function (filtered_content) {
          // Build Lightbox content
          var template = "<div class=\"lightbox__content lightbox__content--".concat(filtered_content.type, " ").concat(options.custom_class, "\">\n <div class=\"lightbox__close fi flaticon-cancel\">{close_button}</div>\n                                                                <div class=\"lightbox__content_inner\">\n                                  {content}\n                                </div>\n                            </div>");
          var lightbox_html = template.replace('{close_button}', options.close_button || '');

          if (filtered_content.type === 'node' && Node.prototype.isPrototypeOf(filtered_content.filtered_content)) {
            lightbox_node.innerHTML = lightbox_html.replace('{content}', '');
            lightbox_node.querySelector('.lightbox__content_inner').appendChild(filtered_content.filtered_content);
          } else {
            lightbox_html = lightbox_html.replace('{content}', filtered_content.filtered_content); // Append lightbox to the body

            lightbox_node.innerHTML = lightbox_html;
          } // Add arrows if it's the case


          addArrows(options);
          resolve();
        })["catch"](function (error) {
          reject(error);
        });
      });
    };
    /**
     * Set the options for the lightbox
     * @param {object} options The object with all the options for the lightbox
     */


    public_methods.setOptions = function () {
      // Setting options if they are not set
      if (!options) {
        options = {};
      } // Checking if options is an object


      if (_typeof(options) === 'object') {
        // Overriding options
        options = Object.assign({}, defaults, options);
      } else if (options) {
        console.warn('Options must be an object.');
      }
    };
    /**
     * Set the triggers for the lightbox based on the trigger option
     */


    public_methods.setTrigger = function () {
      if (!options.trigger) {
        return;
      } // Checking the type of target
      // nodes, nodeslists and selectors are allowed


      var nodes = '';

      if (_typeof(options.trigger) === 'object' && Node.prototype.isPrototypeOf(options.trigger)) {
        nodes.push(options.trigger);
      } else if (_typeof(options.trigger) === 'object' && NodeList.prototype.isPrototypeOf(options.trigger)) {
        nodes = options.trigger;
      } else if (typeof options.trigger === 'string') {
        nodes = options.trigger;
      } else {
        console.error('The target argument is invalid. It must be a node or a css selector.');
        return;
      } // Creating the listeners


      if (typeof nodes === 'string') {
        // If it's a selector, event delegation is used to listen to the events
        // Generate a string with the options to check if a lightbox
        // with the same options was already created
        var function_name = JSON.stringify(options);

        if (window.lightboxTriggerSetups && window.lightboxTriggerSetups.indexOf(function_name) > -1) {
          // If a lightbox was already called with the same options, just quit
          return false;
        } else {
          // Adding the current set up name to the list of the ones
          // already used
          if (!window.lightboxTriggerSetups) {
            window.lightboxTriggerSetups = [];
          }

          window.lightboxTriggerSetups.push(function_name);
        }

        document.addEventListener(options.trigger_event, function (event) {
          var current_node = event.target;

          while (current_node.parentNode) {
            if (typeof current_node.matches !== 'undefined' && current_node.matches(options.trigger)) {
              event.preventDefault();
              var ligthbox_content = getContentFromTrigger(current_node, options.trigger_attribute);
              public_methods.openLightbox(ligthbox_content);
              break;
            }

            current_node = current_node.parentNode;
          }
        });
      } else {
        // If it's a node or a nodelist, just those elements are used for the listeners
        nodes.forEach(function (node) {
          lightbox_node.addEventListener(options.trigger_event, function (event) {
            event.preventDefault();
            var ligthbox_content = getContentFromTrigger(event.target, options.trigger_attribute);
            public_methods.openLightbox(ligthbox_content);
          });
        });
      }
    }; // If no content is set and just options are passed 
    // to the lightbox


    if (_typeof(content) === 'object') {
      options = Object.assign({}, content);
      content = null;
    } // Initialise options


    public_methods.setOptions(options); // Open the lightbox or listen to triggers

    if (content) {
      // If the content is set, open the ligthbox
      public_methods.openLightbox({
        data: content
      });
    } else if (options.trigger) {
      // If no content is set, listen triggers
      public_methods.setTrigger();
    } else {
      console.warn('No content and no options were set. Are you kidding me?');
      return;
    }

    return public_methods;
  };

  return lightbox;
});

/* File: assets/js/flash/1.5.2/plugins/loadCSS.js */
/*! loadCSS. [c]2020 Filament Group, Inc. MIT License */
(function(w){
	"use strict";
	/* exported loadCSS */
	var loadCSS = function( href, before, media, attributes ){
		// Arguments explained:
		// `href` [REQUIRED] is the URL for your CSS file.
		// `before` [OPTIONAL] is the element the script should use as a reference for injecting our stylesheet <link> before
		// By default, loadCSS attempts to inject the link after the last stylesheet or script in the DOM. However, you might desire a more specific location in your document.
		// `media` [OPTIONAL] is the media type or query of the stylesheet. By default it will be 'all'
		// `attributes` [OPTIONAL] is the Object of attribute name/attribute value pairs to set on the stylesheet's DOM Element.
		var doc = w.document;
		var ss = doc.createElement( "link" );
		var ref;
		if( before ){
			ref = before;
		}
		else {
			var refs = ( doc.body || doc.getElementsByTagName( "head" )[ 0 ] ).childNodes;
			ref = refs[ refs.length - 1];
		}

		var sheets = doc.styleSheets;
		// Set any of the provided attributes to the stylesheet DOM Element.
		if( attributes ){
			for( var attributeName in attributes ){
				if( attributes.hasOwnProperty( attributeName ) ){
					ss.setAttribute( attributeName, attributes[attributeName] );
				}
			}
		}
		ss.rel = "stylesheet";
		ss.href = href;
		// temporarily set media to something inapplicable to ensure it'll fetch without blocking render
		ss.media = "only x";

		// wait until body is defined before injecting link. This ensures a non-blocking load in IE11.
		function ready( cb ){
			if( doc.body ){
				return cb();
			}
			setTimeout(function(){
				ready( cb );
			});
		}
		// Inject link
			// Note: the ternary preserves the existing behavior of "before" argument, but we could choose to change the argument to "after" in a later release and standardize on ref.nextSibling for all refs
			// Note: `insertBefore` is used instead of `appendChild`, for safety re: http://www.paulirish.com/2011/surefire-dom-element-insertion/
		ready( function(){
			ref.parentNode.insertBefore( ss, ( before ? ref : ref.nextSibling ) );
		});
		// A method (exposed on return object for external use) that mimics onload by polling document.styleSheets until it includes the new sheet.
		var onloadcssdefined = function( cb ){
			var resolvedHref = ss.href;
			var i = sheets.length;
			while( i-- ){
				if( sheets[ i ].href === resolvedHref ){
					return cb();
				}
			}
			setTimeout(function() {
				onloadcssdefined( cb );
			});
		};

		function loadCB(){
			if( ss.addEventListener ){
				ss.removeEventListener( "load", loadCB );
			}
			ss.media = media || "all";
		}

		// once loaded, set link's media back to `all` so that the stylesheet applies once it loads
		if( ss.addEventListener ){
			ss.addEventListener( "load", loadCB);
		}
		ss.onloadcssdefined = onloadcssdefined;
		onloadcssdefined( loadCB );
		return ss;
	};
	// commonjs
	if( typeof exports !== "undefined" ){
		exports.loadCSS = loadCSS;
	}
	else {
		w.loadCSS = loadCSS;
	}
}( typeof global !== "undefined" ? global : this ));

/* File: assets/js/flash/1.5.2/libraries/responsive-menu.js */
/**
 * Responsive Menu System
 * ----------------------
 */

var flashResponsiveMenu = function(menu) {
	var self = this;

	self.active_class = 'responsive_menu--in';
	self.menu = document.body.querySelector(menu);
	self.menu_name = self.menu.getAttribute('data-flash-responsive-menu');
	self.transitioning = false;

	self.init();

	return self.menu;
}


/*
/*
		$('body').css({
			position: 'fixed',
			top: scroll_top + 'px'
		});

		var scroll_top = $('body').css('top');
		$('body').css({
			position: 'relative',
			top: 0
		});
 */

/**
 * Init the menu and bind events
 * @return void
 */
flashResponsiveMenu.prototype.init = function() {
	var self = this;

	// Check if the menu is already initialised on this element 
	// and if the menu has a name set
	if(self.menu.classList.contains('js_init--responsive_menu') || !self.menu_name) {
		return false;
	} else {
		self.menu.classList.add('js_init--responsive_menu');
	}

	// Get the trigger
	self.trigger = document.querySelector('[data-flash-responsive-menu-trigger="' + self.menu_name + '"]');

	// Open the menu clicking on the trigger
	self.trigger.addEventListener('click', function(e) {
		e.preventDefault();
		self.open();
	}.bind(this), false);

	// Close the menu clicking on the menu
	self.menu.addEventListener('click', function(e){
		if(!e.target.closest('.responsive_navigation') && self.isActive()) {
			self.close();
		}
	}.bind(this), false);
	
	// Close the menu clicking on the close icon
	self.menu.querySelector('[data-close-responsive-menu]').addEventListener('click', function(e){
		e.preventDefault();
		self.close();
	}.bind(this), false);

	// Open a submenu when the page is loaded
	var clicked_triggers = [];
	document.body.querySelectorAll('.responsive_navigation [data-dropdown] a').forEach(function(anchor){
		var locations = [];

		if(anchor.hasAttribute('href') && window.location.pathname.indexOf(anchor.getAttribute('href')) > -1) {
			var parent_dropdown = anchor.closest('[data-dropdown]');
			var dropdown_name = parent_dropdown.getAttribute('data-dropdown');
			var trigger = document.body.querySelector('[data-dropdown-target="' + dropdown_name + '"]');
			if(clicked_triggers.indexOf(trigger.innerText) == -1) {
				parent_dropdown.style.maxHeight = '';
				parent_dropdown.style.display = 'block';
				trigger.classList.add('open');
				clicked_triggers.push(trigger.innerText);
			}
		}
	});

	/**
	 * SUBMENU STUFF
	 */
	flash.listen(self.menu.querySelectorAll('[data-submenu-target]'), 'click', function(e){
		if(self.transitioning) {
			return;
		}
		self.transitioning = true;
		var clicked_link = e.target;
		var menu = clicked_link.closest('.responsive_navigation__menu');
		
		if(!clicked_link.dataset.submenuTarget) {
			return;
		}

		var submenu = self.menu.querySelector('[data-submenu="' + clicked_link.dataset.submenuTarget + '"]');
		if(!submenu) {
			return;
		}

		// ANIMATING THE MENUS
		// class visible makes the items visible
		// class next is applied to the next menu that will appear from the right
		// move_next, combined with next will make the menu come from the right
		// move_next, not combined with next will make the current menu move out to the left
		// the same idea is applied to the move_prev and prev classes below
		submenu.classList.add('visible');
		submenu.classList.add('next');

		function moveSubmenuNext() {
			// Unbind event so we don't mess up all the things
			submenu.removeEventListener('transitionend', moveSubmenuNext);
			self.transitioning = false;
			setTimeout(function(){
				submenu.classList.remove('move_next');
				submenu.classList.remove('next');
				menu.classList.remove('move_next');
				menu.classList.remove('visible');
			}, 120);
		}

		setTimeout(function(){
			document.querySelector('.responsive_navigation__menus').scrollTop = 0;
			submenu.classList.add('move_next');
			menu.classList.add('move_next');
			// Cleaning up classes when the move transition is over
			submenu.addEventListener('transitionend', moveSubmenuNext);
		}, 80);

		// Binding event to go back (read the info about the "next" animation because criteria is the same)
		// in case a back link is defined
		// back link is defined adding the data-submenu-back attribute to the element you want to use
		// for this purpose
		var back_link = submenu.querySelector('[data-submenu-back]');
		if(back_link) {
			function transitionBack() {
				if(self.transitioning) {
					moveSubmenuNext();
				}
				self.transitioning = true;
				// Animating the menus
				menu.classList.add('visible');
				menu.classList.add('prev');

				setTimeout(function(){
					submenu.classList.add('move_prev');
					menu.classList.add('move_prev');
					menu.addEventListener('transitionend', moveSubmenuPrev);

					function moveSubmenuPrev() {
						// Unbind event so we don't mess up all the things
						menu.removeEventListener('transitionend', moveSubmenuPrev);
						self.transitioning = false;

						setTimeout(function(){
							submenu.classList.remove('visible');
							submenu.classList.remove('move_prev');
							menu.classList.remove('prev');
							menu.classList.remove('move_prev');
						}, 120);
					}
				}, 80);		
				back_link.removeEventListener('click', transitionBack);
			}

			back_link.addEventListener('click', transitionBack);
		}
	});
}

/**
 * Check if this menu is open
 * @return {Boolean} true if the menu is active
 */
flashResponsiveMenu.prototype.isActive = function() {
	var self = this;

	return self.menu.classList.contains(self.active_class)
}

/**
 * Open the menu
 * @return {void}
 */
flashResponsiveMenu.prototype.open = function() {
	var self = this;

	if(self.isActive()) {
		return;
	}

	self.menu.classList.add('responsive_menu--in');
	document.body.classList.add('overlay');

	// No scroll allowed here
	document.body.style.overflowY = 'hidden';
	document.body.style.height = window.offsetHeight;
	document.body.style.top = window.scrollY * -1 + 'px';
	document.body.style.position = 'fixed';
	self.transitioning = true;
	self.menu.querySelector('.responsive_navigation__menu:not([data-submenu])').classList.add('visible');

	setTimeout(function(){
		self.active = true;
		self.transitioning = false;
	}, 200);
}

/**
 * Close the menu
 * @return {void}
 */
flashResponsiveMenu.prototype.close = function() {
	var self = this;

	if(!self.isActive()) return;

	document.body.style.overflowY = 'auto';
	document.body.style.height = 'auto';
	document.body.style.position = 'relative';
	window.scroll(0, parseInt(document.body.style.top) * -1);
	document.body.style.top = '0';

	self.menu.classList.remove('responsive_menu--in');
	document.body.classList.remove('overlay');

	self.active = false;
	setTimeout(function(){
		self.menu.querySelectorAll('.responsive_navigation__menu').forEach(function(menu){
			menu.classList.remove('visible');
		});
	}, 400);
}


/* File: assets/js/flash/1.5.2/core/_flash/_flash.js */
/**
 * Version: 1.0
 */

/**
 * Flash Core Library
 * This is a class with some utilities
 * In the frontend the object used is called "flash"
 */

var flashCore = function() {
	var self = this;
}

flashCore.prototype.breakpoints = {
    small: '0',
    medium: '640',
    large: '1024',
    container: '1230',
    xlarge: '1300'       
}

/**
 * Init the environment for a new page
 */
flashCore.prototype.newPage = function() {
	var self = this;

	// Page Init Functions
	var page_init = {
		callback: function(){
			// Init Features
			flash.scrollTo();
			flash.tabs();
			flash.dropdowns();
			flash.activeLinks();

			// Init the object fit fix for ie11
			flash.objectFitFix();
			flash.iosSrcsetFix();
			flash.initLazySizes();

			// Responsive menu
			if(document.querySelector('[data-flash-responsive-menu]')) {
				var flash_main_menu = new flashResponsiveMenu('[data-flash-responsive-menu]');
			}

			// Lightboxes
			var lightboxes = lightbox({trigger: '[data-lightbox]'});
			var lightboxes_forms = lightbox({trigger: '[data-lightbox-form]', trigger_attribute: 'data-lightbox-form', clone_content: false});
			var lightboxes_video = lightbox({trigger: '[data-lightbox-video]', trigger_attribute: 'data-lightbox-video'});
			
			/**
			 * Browsers Support
			 */
			// Not supported browser message
		    if(navigator.userAgent.match('MSIE 10.0;')) {
					var rootPath = '/';
					try {
							rootPath = document.querySelector('meta[name="rp"]').getAttribute('content');
					} catch(e) {}
		    	document.body.insertAdjacentHTML('afterend', '<div class="browser_message" style="position: fixed; width: 100%; height: 100%; z-index: 1000000; top: 0; background: #142435; text-align: center; font-size: 0.8em; padding-top: 50px;"><div class="wrapper"><img style="width:195px;margin-bottom: 40px;" src="' + rootPath + 'assets/images/design/logo.png" alt="" Logo" /><h1 style="color: white;">Uh Oh! Your browser is too old to view our site.</h1><h2 style="color: white;">Here is what you need to do</h2><p style="color: white;">Please <a style="color:#D11368;" href="http://browsehappy.com/" target="_blank" rel="noopener">upgrade your browser</a> or install <a style="color:#D11368;" href="https://www.google.com/intl/en/chrome/browser/" target="_blank" rel="noopener">Google Chrome</a> to experience this website.</p></div></div><div class="overlay"></div>' );
			}
		}
	};

	// Page init also going back
	var page_init_always = {
		callback: function(){
			flash.selectSync();
		},
		always: true
	}

	self.startup_functions = {
		named: {},
		anonymous: {
			0: page_init,
			1: page_init_always
		}
	};
}

/**
 * Init the flash class and execute the code to init the environment
 * for a new page
 */
if(typeof flash === 'undefined') {
	var flash = new flashCore();
}
flash.newPage();

/* File: assets/js/flash/1.5.2/core/nodes/active.js */
/**
 * v 1.0
 * ADDING "ACTIVE" CLASS TO ANCHORS AND TAGS BASED ON CURRENT URL
 * 
 *
 * Exaples of usage:
 * 
 * 	<a href="/mypage" data-active>Link</a>   								<--- To target a single link
 *
 * 
 *	<div data-active-children>      										<--- To add a list of links in a tag
 *		<a href="/mypage" data-active-self>Link</a>							<--- To set active just on this page and not in childern
 *		<a href="/mypage2" data-active-ignore>Link</a>   					<--- To ignore a link in a list
 *		<a href="/mypage/abc">Link</a>   	
 *		<a href="/mypage2" data-active-exclude="/mypage2/fgh">Link</a>   	<--- To exclude a subpage in particular
 *		<a href="/mypage2/fgh">Link</a>   
 *		<a href="/mypage2/#fgh" data-active-self-strict>Link</a>   			<--- To match just the specific hash				
 *	</div>
 *
 *
 *	<div data-active-on="/my-url|/my-url-2">Fake Link</div>   				<--- To target any tag
 */


flashCore.prototype.activeLinks = function() {
	// Prepares the collection of elements
	var links = document.querySelectorAll('[data-active]:not([data-active-ignore]), [data-active-children] a:not([data-active-ignore]), [data-active-on]:not([data-active-ignore])');

	// Stops if there are no links to watch for
	if (!links.length) {
		return;
	}

	var page_url = typeof flash_original_url == 'undefined' ? window.location.pathname : flash_original_url;

	links.forEach( function(link){
	   	var matches = false;
		
		// Check if already applied
		link.classList.add('js_init--flashActive');

		// Gets the url of the current element
		if (link.getAttribute('data-active-on')) {
			var link_urls = link.getAttribute('data-active-on').split('|');
		} else {
			var link_urls = [link.getAttribute('href')];
		}

		if (link_urls) {
			link_urls.forEach(function( link_url ){
				switch(link_url) {
					// If the link url is to the home
					case '/':
						matches = (page_url === link_url || window.location.pathname === link_url);
						break;
					case '#':
						matches = false;
						break;
					// If the link url is to any other page
					default:
						if(!link_url) {
							break;
						}
						if(link.hasAttribute('data-active-self')) {
							matches = (window.location.pathname === link_url || (link_url.split('#'))[0] === window.location.pathname);
						} else if( link.hasAttribute('data-active-exclude') ){
							matches = ( window.location.pathname.indexOf(link_url) >= 0 && window.location.pathname.indexOf(link.getAttribute('data-active-exclude')) == -1 );
						} else {
							matches = matches || (page_url.indexOf((link_url.split('#'))[0]) >= 0 ||  window.location.pathname.indexOf((link_url.split('#'))[0]) >= 0);
						}

						// If the url must be the same, with no flexibility on the hash (if any hash is present)
						if(link.hasAttribute('data-active-self-strict')) {
							matches = (window.location.pathname + window.location.hash) === link_url;
						}
						break;
				}
			});

			// If url matches, the class is applied
			if (matches) {
				link.classList.add('active');
			}
		}
	});
}

/* File: assets/js/flash/1.5.2/core/animations/compareBreakpoint.js */
flashCore.prototype.compareBreakpoint = function(breakpoint, operators) {
    var self = this;
    var response = false;

    if(breakpoint.split(' ').length == 2) {
        var breakpoint_data = breakpoint.split(' ');
        breakpoint = breakpoint_data[0];

        switch(breakpoint_data[1]) {
            case 'up':
                operators = '>=';
                break;
            case 'down':
                operators = '<=';
                break;
            case 'only':
                operators = '=';
                break;
        }
    }

    if(!self.breakpoints[breakpoint]) {
        console.warn('There is no breakpoint named ' + breakpoint);
        return;
    }

    operators = operators.split('');
    operators.forEach( function(operator){
        if((!operator || operator === '=') && breakpoint === self.currentBreakpoint()) {
            response = true;
        } else if(operator === '>' && self.breakpoints[breakpoint] < window.innerWidth) {
            response = true;
        } else if(operator === '<' && self.breakpoints[breakpoint] > window.innerWidth) {
            response = true;
        }
    });

    return response;
}

/* File: assets/js/flash/1.5.2/core/utils/country.js */
/**
 * v 1.0
 * JS User Country Detector
 * ------------------------
 */

flashCore.prototype.getUserCountry = function(callback) {
	if(readCookie('user_country')) return callback(readCookie('user_country'));

	var request = new XMLHttpRequest();
	request.open('GET', 'http://ip-api.com/json/?callback=?', true);

	request.onload = function() {
	  if (request.status >= 200 && request.status < 400) {
	    // Success!
	    var data = JSON.parse(request.responseText);
	    createCookie('user_country', data.countryCode);
        return callback(data.countryCode);
	  } else {
	    // We reached our target server, but it returned an error

	  }
	};

	request.onerror = function() {
	  // There was a connection error of some sort
	};

	request.send();
}

// Usage:
// ======
// flash.getUserCountry(function(country) {
// 	console.log('user country: ' + country);
// });

/* File: assets/js/flash/1.5.2/core/cookies/createCookie.js */
flashCore.prototype.createCookie = function(name,value,days) {
    if (days) {
        var date = new Date();
        date.setTime(date.getTime()+(days*24*60*60*1000));
        var expires = "; expires="+date.toGMTString();
    }
    else var expires = "";
    document.cookie = name+"="+value+expires+"; path=/";
}

/* File: assets/js/flash/1.5.2/core/animations/currentBreakpoint.js */
flashCore.prototype.currentBreakpoint = function() {
    var self = this;

    var breakpoint = null;
    var keys = Object.keys(self.breakpoints);

    keys.forEach(function(key, index){
        if(!breakpoint) {
            if(index === keys.length - 1 || (window.innerWidth >= self.breakpoints[key] && window.innerWidth < self.breakpoints[keys[index + 1]])) {
                breakpoint = key;
            }
        }
    });

    return breakpoint;
}

/* File: assets/js/flash/1.5.2/core/menu/dropdowns.js */
/**
 * v 1.0
 * OPENING AND CLOSING DROPDOWN MENUS
 *
 * Usage:
 * 1) Add data-dropdown-target to the menu trigger and set a custom value (i.e. data-dropdown-target="mymenu1").
 * 2) Add data-dropdown with the same value as the trigger to the actual dropdown.
 * 3) If you want the dropdown to open also on hover you can add data-dropdown-hover to the trigger. 
 *
 * Example:
 * 	<a href="#" data-dropdown-target="my-menu-1" data-dropdown-hover>Menu Opener</a>
 *	<div data-dropdown="my-menu-1">
 *		<a href="/mypage">Link</a>
 *		<a href="/mypage2">Link</a> 
 *	</div>
 */

flashCore.prototype.dropdowns = function(){
	if(!document.body.classList.contains('js_init--dropdowns')) {
		document.body.classList.add('js_init--dropdowns');

		var currently_hovering = null; 		// Name of the hovered element
		var is_closing_menu = false;		// Is currently closing menus
		var opening_menu = null; 				// Name of the opening menu
		var dropdowns_hover_selector = ''; 
		var max_wating_time = 600; 			// Time waited before checking if the user is out of the menu item
		var hover_open_delay = 200;
		var close_timeout = null;
		var open_timeout = null;

		var dropdowns = document.querySelectorAll('[data-dropdown]');
		var dropdowns_with_hover = document.querySelectorAll('[data-dropdown-hover]');
		var dropdowns_triggers = document.querySelectorAll('[data-dropdown-target]');
		if(!dropdowns_triggers.length) {
			return;
		}

		/**
		 * SHARED METHODS FOR OPENING AND CLOSING
		 */
		
		// Opens dropdown
		function openDropdown(trigger) {
			opening_menu = trigger.getAttribute('data-dropdown-target');
			var dropdown = document.querySelector('[data-dropdown="' + opening_menu + '"]');

			// In case the dropdown doesn't exist
			if(!dropdown) {
				console.warn('The [data-dropdown="' + opening_menu + '"] dropdown does not exist');
				return;
			}

			// Closing other dropdowns
			var opened_menus = [];
			var all_opened_menus = dropdown.parentNode.parentNode.querySelectorAll('.open[data-dropdown-target]');
			all_opened_menus.forEach(function(menu){
				if(!menu.isSameNode(trigger)) {
					opened_menus.push(menu);
				}
			});

			if (!is_closing_menu) {
				is_closing_menu = true;
				clearTimeout(close_timeout);
				if(opened_menus.length) {
					opened_menus.forEach( function(dropdown){
						closeDropdown(dropdown);
					});
				}
				is_closing_menu = false;
			}

			// Show dropdown
			dropdown.style.display = 'block';	
			trigger.classList.add('open');	

			if(dropdown.classList.contains('megamenu')) {
				document.body.classList.add('overlay--megamenu');
			}
		};

		// Closes dropdown
		function closeDropdown(element) {
			var name = element.hasAttribute('data-dropdown') ? element.getAttribute('data-dropdown') : element.getAttribute('data-dropdown-target');
			var trigger = document.querySelector('[data-dropdown-target="' + name + '"]');

			// Close the dropdown
			var dropdown = document.querySelector('[data-dropdown="' + trigger.dataset.dropdownTarget + '"]');
			dropdown.style.display = 'none';
			
			// Remove the open class from the trigger
			trigger.classList.remove('open');	
			
			if(dropdown.classList.contains('megamenu')) {
				document.body.classList.remove('overlay--megamenu');
			}	
		};



		/**
		 * SETUP AND CLICK EVENTS
		 */
		// Hiding all the dropdowns
		for (var i = dropdowns.length - 1; i >= 0; i--) {
			dropdowns[i].style.display = 'none';
		}
		// Triggering menu open/close on click
		for (var i = dropdowns_triggers.length - 1; i >= 0; i--) {
			if(!dropdowns_triggers[i].hasAttribute('data-dropdown-hover')) {			
				flash.listen(dropdowns_triggers[i], 'click', function(e){
					e.preventDefault();

					if (e.target.classList.contains('open') ) {
						closeDropdown(e.target);
					} else {
						openDropdown(e.target);
					}
				});
			}
		}



		/**
		 * HOVER TRIGGER
		 */
		// Getting a list of selectors for hoverable drop down menus
		var dropdowns_hover = [];
		dropdowns_with_hover.forEach(function(el){
			var menu_name = el.getAttribute('data-dropdown-target');
			var this_menu = document.querySelector('[data-dropdown="' + menu_name + '"]');
			if(menu_name && this_menu) {
				dropdowns_hover.push('[data-dropdown="' + menu_name + '"]');
				this_menu.setAttribute('data-dropdown-hover','');
			}
			dropdowns_hover_selector = dropdowns_hover.join(',');
		});

		// In case there are hoverable dropdown menus
		if(dropdowns_hover_selector) {
			// Triggering menu open/close on hover intent
			var hover_triggers = document.querySelectorAll('[data-dropdown-hover]:not([data-dropdown])');
			flash.listen(hover_triggers, 'mouseenter', function(event){
				open_timeout = setTimeout(function(){
					openDropdown(event.target);
				}, hover_open_delay);
			});
			flash.listen(hover_triggers, 'mouseleave', function(event){
				var trigger = event.target;
				var name = trigger.dataset.dropdownTarget;

				clearTimeout(open_timeout);
				// Waits a little bit in case the user is moving from the link to the dropdown
				close_timeout = setTimeout(function(){
					if (currently_hovering !== name) {
						closeDropdown(trigger);
					}
				}, max_wating_time);
			});

			// Detecting name of current dropdown hovered (both trigger and actual menu)
			// so if a user goes from the link to the dropdown (and the opposite) the dropdown won't be closed
			var dropdowns_hover_filtered = document.querySelectorAll(dropdowns_hover_selector);
			flash.listen(dropdowns_hover_filtered, 'mouseenter', function(event){
				var menu = event.target;
				var name = menu.getAttribute('data-dropdown');
				currently_hovering = name;
				clearTimeout(close_timeout);
			});
			flash.listen(dropdowns_hover_filtered, 'mouseleave', function(event){	
				var menu = event.target;
				var name = menu.getAttribute('data-dropdown');
				currently_hovering = '';
			});

			// Detecting just dropdown hovering
			flash.listen(dropdowns, 'mouseleave', function(event){
				var menu = event.target;
				var name = menu.getAttribute('data-dropdown');
				if(!menu.hasAttribute('data-dropdown-hover')) {
					return;
				}
				// Waits a little bit in case the user is moving from the dropdown to the link
				close_timeout = setTimeout(function(){
					if (currently_hovering != name) {
						closeDropdown( menu );
					}
				}, max_wating_time);
			});
		}
	}
}

/* File: assets/js/flash/1.5.2/core/cookies/eraseCookie.js */
flashCore.prototype.eraseCookie = function(name) {
    this.createCookie(name,"",-1);
}

/* File: assets/js/flash/1.5.2/core/animations/fadeIn.js */
flashCore.prototype.fadeIn = function(el, timing, callback) {
    var self = this;

    if(!timing) {
        timing = 500;
    }
    var animation_timing_ms = timing;
    var transitions = el.style['transition'];

    if(!el.style.opacity) {
        el.style.opacity = 0;
    }
    el.style['transition']          = 'opacity ' + animation_timing_ms + 'ms ease-in-out';
    if(el.style.display === 'none' || !el.style.display) {
        el.style.display                = 'block';
    }
    setTimeout(function(){
        el.style.opacity                = 1;  
    }, 1);

    if(callback) {
        setTimeout(function(){
            el.style.transitions = transitions;
            callback();
        }, timing);
    }    
}

/* File: assets/js/flash/1.5.2/core/animations/fadeOut.js */
flashCore.prototype.fadeOut = function(el, timing, callback) {
    var self = this;

    if(!timing) {
        timing = 500;
    }
    var animation_timing_ms = timing;
    var transitions = el.style['transition'];

    el.style['transition']          = 'opacity ' + animation_timing_ms + 'ms ease-in-out';
    el.style.opacity                = 0;  

    if(callback) {
        setTimeout(function(){
            el.style.transitions = transitions;
            callback();
        }, timing);
    }    
}

/* File: assets/js/flash/1.5.2/core/animations/getHeight.js */
flashCore.prototype.getHeight = function(el) {
    var el_style      = window.getComputedStyle(el),
        el_display    = el_style.display,
        el_position   = el_style.position,
        el_visibility = el_style.visibility,
        el_transition = el.style['transition'],
        el_max_height = el_style.maxHeight,

        wanted_height = 0;


    // if its not hidden we just return normal height
    if(el_display !== 'none' && el_max_height.replace('px', '').replace('%', '') !== '0') {
        return el.offsetHeight;
    }

    // the element is hidden so:
    // making the el block so we can meassure its height but still be hidden
    el.style.position   = 'absolute';
    el.style.visibility = 'hidden';
    el.style.display    = 'block';
    el.style.transition = '';
    el.style.maxHeight  =  'none';
    var parent_position = el.parentNode.style.position;
    if(!parent_position) {
        el.parentNode.style.position = 'relative';
    }

    wanted_height     = el.offsetHeight;

    // reverting to the original values
    el.parentNode.style.position = parent_position;
    el.style.display    = el_display;
    el.style.position   = el_position;
    el.style.visibility = el_visibility;
    el.style.transition = el_transition;
    el.style.maxHeight = el_max_height;
    
    return wanted_height;
};

/* File: assets/js/flash/1.5.2/core/utils/getUrlParameter.js */
/**
 * v 1.0
 * Get url parameter
 */
flashCore.prototype.getUrlParameter = function(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    var regex = new RegExp('[\\?&]' + name + '=([^&#]*|&amp;)');
    var results = regex.exec(location.search);
    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
};

/* File: assets/js/flash/1.5.2/core/utils/getVideoBackgroundColor.js */
flashCore.prototype.getVideoBackgroundColor = function(video, callback) {

    // Setup a canvas element in front of the video
    if(!video) return;
    video.style.opacity = 0;
    video.innerHTML = video.innerHTML + '';
    video.insertAdjacentHTML('afterend', '<canvas id="video-buffer" style="position: absolute; left: 0; top: 0;"></canvas>');
    var canvas = video.parentNode.querySelector('canvas#video-buffer');
    var buffer = canvas.getContext('2d');
    buffer.scale(2, 2);

    // Make the canvas same size than the video (with retina support)
    function fitCanvas() {
        canvas.setAttribute('width', video.offsetWidth * 2);
        canvas.setAttribute('height', video.offsetHeight * 2);
        canvas.style.width = video.offsetWidth + 'px';
        canvas.style.height = video.offsetHeight + 'px';
    } fitCanvas();
    window.onresize = function() { fitCanvas() };

    // Re-render the video on the canvas element to fix the colors
    function update() {
        fitCanvas();
        buffer.drawImage(video, 0, 0, video.offsetWidth * 2, video.offsetHeight * 2);
        window.requestAnimationFrame(update);
    }
    window.requestAnimationFrame(update);

    // Get the background color and clear the check interval
    var interval = setInterval(function() {
        if(!video.paused) {
            var _canvas = document.createElement('canvas');
            _canvas.width = 10;
            _canvas.height = 10;
            var _ctx = _canvas.getContext('2d');
            _ctx.drawImage(canvas, 0, 0, 10, 10, 0, 0, 10, 10);
            var p = _ctx.getImageData(0, 0, 10, 10).data;
            callback('rgb(' + p[60] + ', ' + p[61] + ', ' + p[62] + ')');
            clearInterval(interval);
        }
    }, 100);

};


/* File: assets/js/flash/1.5.2/core/animations/getWidth.js */
flashCore.prototype.getWidth = function(el) {
    var el_style      = window.getComputedStyle(el),
        el_display    = el_style.display,
        el_position   = el_style.position,
        el_visibility = el_style.visibility,
        el_transition = el.style['transition'],
        el_max_height = el_style.maxHeight,
        el_max_width = el_style.maxHeight,

        wanted_width = 0;


    // if its not hidden we just return normal height
    if(el_display !== 'none' && el_max_height.replace('px', '').replace('%', '') !== '0') {
        return el.offsetWidth;
    }

    // the element is hidden so:
    // making the el block so we can meassure its height but still be hidden
    el.style.position   = 'absolute';
    el.style.visibility = 'hidden';
    el.style.display    = 'block';
    el.style.transition = '';
    el.style.maxHeight  =  'none';
    el.style.maxWidth  =  'none';
    var parent_position = el.parentNode.style.position;
    if(!parent_position) {
        el.parentNode.style.position = 'relative';
    }

    wanted_height     = el.offsetWidth;

    // reverting to the original values
    el.parentNode.style.position = parent_position;
    el.style.display    = el_display;
    el.style.position   = el_position;
    el.style.visibility = el_visibility;
    el.style.transition = el_transition;
    el.style.maxHeight = el_max_height;
    el.style.maxWidth = el_max_width;
    
    return wanted_width;
};

/* File: assets/js/flash/1.5.2/core/nodes/hasEnteredViewport.js */
/*!
 * Determine if an element is about to enter the viewport or if it is above
 * @param  {Node}    elem The element
 * @param  {Integer}    offset Offset in pixel to anticipate or delay the result
 * @return {Boolean}      Returns true if element is in the viewport
 */
flashCore.prototype.hasEnteredViewport = function (elem, offset) {
	if(!elem) {
		console.warn('elem is not set');
		return false;
	}
	offset = offset || 0;
	var distance = elem.getBoundingClientRect();
	return (distance.top <= window.innerHeight + offset && distance.top > 0) || (distance.bottom <= window.innerHeight + offset && distance.bottom >= 0);
};

/* File: assets/js/flash/1.5.2/core/images/initLazySizes.js */
flashCore.prototype.initLazySizes = function(){
    // Lazysizes
    // ===========================
    function initLazySizes() {
    	if(!document.body.classList.contains('js_init--lazyload')) {
    		document.body.classList.add('js_init--lazyload');
			document.addEventListener('lazybeforeunveil', function(e){
				var target = e.target;
				// Lazy loading for background images
				var bg = target.getAttribute('data-bg');
				if(bg){
					e.target.style.backgroundImage = 'url(' + bg + ')';
				}
				// Lazy loading map
				if(target.classList.contains('block_map') && document.body.querySelector('#map')) {
					searchMap = new searchInterface();
				}
			});
			if('objectFit' in document.documentElement.style === false) {
				document.addEventListener('lazyloaded', function(e) {
					flash.objectFitFix();
				});
				if(!document.querySelectorAll('.lazyload') || document.querySelectorAll('.lazyload').length == 0) {
					flash.objectFitFix();
				}
			}
			lazySizes.init();
    	}
    }
    window.addEventListener('scroll', function(){
    	initLazySizes();
    });

    initLazySizes();
}

/* File: assets/js/flash/1.5.2/core/images/iosSrcsetFix.js */
flashCore.prototype.iosSrcsetFix = function() {	
	if((/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream) || navigator.userAgent.search("Safari") >= 0) {
		document.querySelectorAll('[srcset]').forEach(function(image){
			if(!image.parentNode.classList.contains('contain_image') && !image.parentNode.classList.contains('cover_image')) {
				image.parentNode.innerHTML = image.parentNode.innerHTML + '';
			}
		});
	}
}

/* File: assets/js/flash/1.5.2/core/nodes/isElementAbove.js */
/*!
 * Determine if an element is above the scrolled position
 * @param  {Node}    	element 	The element
 * @param  {Boolean}  	partially_above 	true if you want to check if even part of the element is above the viewport
 * @return {Boolean}  	true if the element is above the viewpoirt
 */
flashCore.prototype.isElementAbove = function (element, partially_above) {
	if(!element) {
		return;
	}
	
	var element_top = element.getBoundingClientRect().top;
	var element_height = element.getBoundingClientRect().height;
	if(partially_above) {
		return (element_top < 0 && (element_top + element_height) >= 0);
	} else {
		return ((element_top + element_height) < 0);
	}
};

/* File: assets/js/flash/1.5.2/core/nodes/isInViewport.js */
/*!
 * Determine if an element is in the viewport
 * (c) 2017 Chris Ferdinandi, MIT License, https://gomakethings.com
 * @param  {Node}    elem The element
 * @return {Boolean}      Returns true if element is in the viewport
 */
flashCore.prototype.isInViewport = function (elem, offset) {
	offset = offset || 0;
	var distance = elem.getBoundingClientRect();

	return (
		distance.top >= 0 &&
		distance.bottom <= (window.innerHeight || document.documentElement.clientHeight)
	);
};

/* File: assets/js/flash/1.5.2/core/events/listen.js */
/**
 * Add an event listener to a node, a node list and eventually with delegation
 * @param  {Node|Nodelist}  	nodes    the node/nodes to bind the event
 * @param  {String}   			event    the name of the vent
 * @param  {Function} 			fn       the callback of the event
 * @param  {string}   			selector the selector used for delegation
 * @return {void}            	
 */
flashCore.prototype.listen = function(nodes, event, fn, selector) {
	//var self = this;
	var callback = fn;
	
	// Return if nodes is not a nodelist neither a node
	if(!nodes || (!NodeList.prototype.isPrototypeOf(nodes) && !Node.prototype.isPrototypeOf(nodes) && !HTMLCollection.prototype.isPrototypeOf(nodes) && typeof nodes !== 'string')) {
		return;
	} else if(Node.prototype.isPrototypeOf(nodes)) {
		nodes = [nodes];
	} else if(typeof nodes === 'string') {
		nodes = document.querySelectorAll(nodes);
	} else if(HTMLCollection.prototype.isPrototypeOf(nodes)) {
		nodes = Array.prototype.slice.call(nodes);
	}

	// Checking if delegation is needed
	var delegate = !!selector;
	if(delegate) {
		callback = function(event) {
			var target = event.target;
			var found = false;
			while(target.parentNode) {
				if(typeof target.matches !== 'undefined' && target.matches(selector)) {
					fn(event, target);
				} 
				target = target.parentNode;
			}
		}
	}

	// Add Listeners
	var events = event.split(' ');
    for (var i = 0, len = nodes.length; i < len; i++) {
    	events.forEach(function(single_event){
 			nodes[i].addEventListener(single_event, callback);
    	});
    }
}

/* File: assets/js/flash/1.5.2/core/utils/loadScript.js */
/**
 * Lazy load a script appending a script tag to the body
 * 
 * @param  {string}   url         the url of the script
 * @param  {Function} callback    the code to execute when the script is loaded
 *
 * example: flash.loadScript('https://cdn.jsdelivr.net/npm/vue/dist/vue.js', function(){alert('Vue.js loaded')});
 */
flashCore.prototype.loadScript = function(url, callback, force) {
    var self = this;

	// Checking if the script is already loaded in the current window
	if(typeof window.loadedScripts === 'undefined') {
		window.loadedScripts = [];
	}
	if(window.loadedScripts.indexOf(url) == -1 || force) {
		// Preparing and appending the script tag
		var script 		= document.createElement('script');
		script.type 	= 'text/javascript';
		script.src 		= url;
		script.async 	= 'true';
		script.defer 	= 'true';
		document.body.appendChild(script);
		if(typeof callback === 'function') {
			script.addEventListener('load', callback);
		}
	} else {
		if(typeof callback === 'function') {
			callback();
		}
	}
	
	// Adding the script to the loaded ones array
	window.loadedScripts.push(url);
}

/* File: assets/js/flash/1.5.2/core/images/objectFitFix.js */
/**
 * v 1.0
 */
flashCore.prototype.objectFitFix = function() {	
	if('objectFit' in document.documentElement.style === false) {
		document.querySelectorAll('.cover_image:not(.js_init--object_fit), .contain_image:not(.js_init--object_fit)').forEach(function(container){
			var img = container.querySelector('img');
			if(!img || img.classList.contains('lazyload')) {
				return;
			}
			container.classList.add('js_init--object_fit');
			var img_url = img.getAttribute('src');
			img.style.display = 'none';
			var img_div = document.createElement('div');
			img_div.classList.add('img');
			img.parentNode.appendChild(img_div);
			img_div.style.backgroundImage = 'url(\'' + img_url + '\')';
		});
	} else {
		document.querySelectorAll('.cover_image, .contain_image').forEach(function(el){
			el.innerHTML = el.innerHTML + '';
		});
	}
}

/* File: assets/js/flash/1.5.2/core/utils/passiveSupported.js */
flashCore.prototype.getPassive = function() {
  var passiveSupported = false;

  try {
    var options = {
      get passive() { // This function will be called when the browser
                      //   attempts to access the passive property.
        passiveSupported = true;
        return false;
      }
    };
  
    window.addEventListener("test", null, options);
    window.removeEventListener("test", null, options);
  } catch(err) {
    passiveSupported = false;
  }

  return passiveSupported ? { passive: true } : false;
}

/* File: assets/js/flash/1.5.2/core/cookies/readCookie.js */
flashCore.prototype.readCookie = function(name) {
    var nameEQ = name + "=";
    var ca = document.cookie.split(';');
    for(var i=0;i < ca.length;i++) {
        var c = ca[i];
        while (c.charAt(0)==' ') c = c.substring(1,c.length);
        if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length,c.length);
    }
    return null;
}

/* File: assets/js/flash/1.5.2/core/events/ready.js */
/**
 * Binding a callback on page load, taking care of the execution status for
 * instantclick cache on back to history event. It's used to bind the js
 * for the scripts of the blocks.
 * @param  {Function} fn         	callback
 * @param  {string}   label 		the name of the block or a unique string associated to the scritp
 * @return {void}              
 */
flashCore.prototype.ready = function(fn, label, options) {
	var self = this;
	var defaults = {};
	if(typeof label === 'object') {
		options = Object.assign({}, label);
		label = null;
	}
	settings = Object.assign({callback: fn}, defaults, options);

	if(label) {
		self.startup_functions.named[label] = settings;
	} else {
		var key = Object.keys(self.startup_functions.anonymous).length;
		self.startup_functions.anonymous[key] = settings;
	}
}

/* File: assets/js/flash/1.5.2/core/utils/scrollto.js */
// Vanilla JavaScript Scroll to Anchor
// @ https://perishablepress.com/vanilla-javascript-scroll-anchor/
flashCore.prototype.scrollTo = function(){
	function flashScrollAnchors(e, respond) {
		var distanceToTop = function(el) {return Math.floor(el.getBoundingClientRect().top - 100)};
		e.preventDefault();
		var targetID = (respond) ? respond.getAttribute('href') : this.getAttribute('href');
		var targetAnchor = document.querySelector(targetID);
		if (!targetAnchor) return;
		var originalTop = distanceToTop(targetAnchor);
		window.scrollBy({ top: originalTop, left: 0, behavior: 'smooth' });
		var checkIfDone = setInterval(function() {
			var atBottom = window.innerHeight + window.pageYOffset >= document.body.offsetHeight - 2;
			if (distanceToTop(targetAnchor) === 0 || atBottom) {
				targetAnchor.tabIndex = '-1';
				targetAnchor.focus();
				window.history.pushState('', '', targetID);
				clearInterval(checkIfDone);
			}
		}, 100);
	}	

	var links = document.querySelectorAll('.scroll');
	links.forEach(function(each){return (each.onclick = flashScrollAnchors)});
}

/* File: assets/js/flash/1.5.2/core/tabs/select-sync.js */
flashCore.prototype.selectSync = function(){
	var sync_select = document.querySelectorAll('[data-sync-select]');

	if(sync_select.length) {
		var prevent_refresh = false; // prevents select -> button -> select infinite loop

		sync_select.forEach( function(select){

			var settings = select.getAttribute('data-sync-select').split('|');
			var links_wrapper = document.querySelector(settings[0]); // The buttons list
			var links = document.querySelectorAll('[' + settings[1] + ']');

			// If settings are not correct, the function just returns
			if(settings.length != 2 || !links_wrapper || links.length == 0) {
				return;
			}

			setTimeout(function(){
				prevent_refresh = true;
				if(links_wrapper.querySelector('.active[' + settings[1] + ']')) {
					var item_value = links_wrapper.querySelector('.active[' + settings[1] + ']').getAttribute(settings[1]);
					if(select.querySelector('option[value="' + item_value + '"]')) {
						select.value = item_value;
					}
				}
				prevent_refresh = false;
			}, 50);	

			// Sync select --> buttons
			select.addEventListener('change', function(event){
				if ( prevent_refresh ) {
					return;
				}

				var current_sector = event.target.value;
				if(settings[1] == 'href') {
					window.location.href = current_sector
                } else {
                    document.querySelector('[' + settings[1] + '="' + current_sector + '"]').click();
                }
			});

			// Sync buttons --> select
			flash.listen(links_wrapper, 'click', function(){
				var current_sector = event.target.getAttribute(settings[1]);
				prevent_refresh = true;
				select.value = current_sector;
				prevent_refresh = false;
			}, '[' + settings[1] + ']');
		});
	}
}

/* File: assets/js/flash/1.5.2/core/utils/serialize.js */
/**
 * Serialize an object
 * @param  {object} 	o   	the object 
 * @return {string}  		the serialized data
 */
flashCore.prototype.serialize = function(o) {
    return param(o);
}

/* File: assets/js/flash/1.5.2/core/utils/serializeForm.js */
/**
 * Serialise the data in a form
 * @param  {node} 	form   	the form 
 * @return {string}  		the serialised data
 */
flashCore.prototype.serializeForm = function(form) {
	// Setup our serialized data
	var serialized = [];

	// Loop through each field in the form
	for (var i = 0; i < form.elements.length; i++) {

		var field = form.elements[i];

		// Don't serialize fields without a name, submits, buttons, file and reset inputs, and disabled fields
		if (!field.name || field.disabled || field.type === 'file' || field.type === 'reset' || field.type === 'submit' || field.type === 'button') continue;

		// If a multi-select, get all selections
		if (field.type === 'select-multiple') {
			for (var n = 0; n < field.options.length; n++) {
				if (!field.options[n].selected) continue;
				serialized.push(encodeURIComponent(field.name) + "=" + encodeURIComponent(field.options[n].value));
			}
		}

		// Convert field data to a query string
		else if ((field.type !== 'checkbox' && field.type !== 'radio') || field.checked) {
			serialized.push(encodeURIComponent(field.name) + "=" + encodeURIComponent(field.value));
		}
	}

	return serialized.join('&');
}

/* File: assets/js/flash/1.5.2/core/animations/slideDown.js */
flashCore.prototype.slideDown = function(el, timing, callback) {
    var self = this;

    if(!timing) {
        timing = 500;
    }
    var animation_timing_ms = timing;

    var el_max_height = 0;
    var transitions = el.style['transition'];

    el.style.display               = 'block';
    el_max_height                  = self.getHeight(el) + 'px';
    el.style['transition']         = 'max-height ' + animation_timing_ms + 'ms ease-in-out';
    el.style.overflowY             = 'hidden';
    el.style.maxHeight             = '0';

    setTimeout(function() {
        el.style.maxHeight = el_max_height;
    }, 10);

    setTimeout(function(){
        el.style.transitions = transitions;
        
        if(callback) {
            callback();
        }
    }, timing);
}

/* File: assets/js/flash/1.5.2/core/animations/slideToggle.js */
flashCore.prototype.slideToggle = function(el, timing, callback) {
    var self = this;

    var el_max_height = 0;
    if(el.style.maxHeight.replace('px', '').replace('%', '') === '0' || el.style.maxHeight.replace('px', '').replace('%', '') === '') {
        self.slideDown(el, timing, callback);
    } else {
        self.slideUp(el, timing, callback);
    }
}

/* File: assets/js/flash/1.5.2/core/animations/slideUp.js */
flashCore.prototype.slideUp = function(el, timing, callback) {
    var self = this;

    if(!timing) {
        timing = 500;
    }
    var animation_timing_ms = timing;

    var transitions = el.style['transition'];

    el.style.maxHeight             = el.offsetHeight + 'px';
    el.style['transition']         = 'max-height ' + animation_timing_ms + 'ms ease-in-out';
    el.style.overflowY             = 'hidden';  
    setTimeout(function(){
        el.style.maxHeight         = '0';
    }, 1);

    setTimeout(function(){
        el.style.transitions = transitions;
        if(callback) {
            callback();
        }
    }, timing + 1);
}

/* File: assets/js/flash/1.5.2/core/utils/slugify.js */
// v 1.0
// Slugify Plugin
// Usage Example:
// <%= plugins.slugify('Test Here !') %>
// Will return: test-here
flashCore.prototype.slugify = function(text) {
    return text.toString().toLowerCase().trim()
        .replace(/\s+/g, '-')           // Replace spaces with -
        .replace(/&/g, '-and-')         // Replace & with 'and'
        .replace(/[^\w\-]+/g, '')       // Remove all non-word chars
        .replace(/\-\-+/g, '-');        // Replace multiple - with single -
};

/* File: assets/js/flash/1.5.2/core/_flash/start.js */
/**
 * Version: 1.0
 */

/**
 * Execute all the callbacks bound to the DOMContentLoaded event with the flash.ready method.
 * @return {void}
 */
flashCore.prototype.start = function() {
	var self = this;
	// Event to say that Flash frontend core has been initialised
	var flashReadyEvent = new CustomEvent('flashReady');
	document.dispatchEvent(flashReadyEvent);

	// GTM
	if (!window._gaReady) {
		self.waitCondition(
			function(){
				return typeof ga !== 'undefined';
			},
			function(){
							if(document.querySelector('meta[name="gtm"]')) {
									var gtm_id = document.querySelector('meta[name="gtm"]').getAttribute('content');
									window.dataLayer = window.dataLayer || [];
									function gtag(){dataLayer.push(arguments);}
									gtag('js', new Date());
									gtag('config', gtm_id);
									window._gaReady = true
							}
			}
		)
	} else {
		// Replaces ga('send' [...]) which was actually doing nothing
		// on page change as ga was automatically sending pageview on script load
		if (ga.getAll) {
			var trackers = ga.getAll();
			trackers.forEach(function (tracker) {
				tracker.send('pageview', location.pathname + location.search)
			})
		}
	}

	// Check if the node inserted in each page is there
	// if it's not there it means it's a new page
	// otherwise it's an old page loaded with the back button so the code won't
	// be executed at all
    var flash_executed_js = document.querySelector('.flash_executed_js');

	// Executing the js from the blocks
	(Object.keys(self.startup_functions.named) || []).forEach(function(key){
		if(key.indexOf('.') > -1) {
			var blocks = document.querySelectorAll(key);
		} else {
			var blocks = document.querySelectorAll('.' + key + ',' + key);
		}
		if(flash_executed_js && !self.startup_functions.named[key].always) {
			return;
		}
		(blocks || []).forEach(function(block) {
			// Init the js of a block just when it's entering the viewport
			if(self.startup_functions.named[key].in_viewport) {
				// Init an object with all the callbacks for
				// the js that has to be executed JUST when the block is
				// about to enter the viewport
				if(!window.flash_startup_functions) {
					window.flash_startup_functions = {};
				}

				// Function that check if the block is in the viewport
				// and eventually execute the code
				if(window.flash_startup_functions[key]) {
					window.removeEventListener('scroll', window.flash_startup_functions[key]);
				}
				window.flash_startup_functions[key] = function () {
					// the default offset (200px above the block) can be customised with in_viewport_offset
					if(flash.hasEnteredViewport(block, (self.startup_functions.named[key].in_viewport_offset || 200))) {
						window.removeEventListener('scroll', window.flash_startup_functions[key]);
						self.startup_functions.named[key].callback(block);	
					}
				}

				// Listeners to check when the element will be in the vieport
				window.removeEventListener('scroll', window.flash_startup_functions[key]);
				window.addEventListener('scroll', window.flash_startup_functions[key]);
				window.flash_startup_functions[key]();
			} else {
				// If no settings are set, execute the function straight away
				self.startup_functions.named[key].callback(block);	
			}
		});
	});

	// Executing the js from the blocks without body class manipulation
	(Object.keys(self.startup_functions.anonymous) || []).forEach(function(key){
		if(flash_executed_js && !self.startup_functions.anonymous[key].always) {
			return;
		}
		self.startup_functions.anonymous[key].callback();
	});

	if(!flash_executed_js) {
		// Insert the node used to check if it's an old page on page load
		flash_executed_js = document.createElement('div');
		flash_executed_js.className = 'flash_executed_js';
		document.body.appendChild(flash_executed_js);
		// window.dataLayer.push({'event': 'optimize.activate'});
	}
}

/* File: assets/js/flash/1.5.2/core/tabs/tabs.js */
/**
 * v 1.0
 * Simple JS Tabs System
 * ---------------------
 */

 /**
  *
  * Example
  *
  * <div>
  *  <span data-tab-trigger="tab1">Tab 1</span>
  *  <span data-tab-trigger="tab2">Tab 2</span>
  * </div>
  *
  *
  * <div>
  *  <div data-tab="tab1" data-tab-accordion>
  *   <div data-tab="tab1" data-tab-accordion-trigger>
  *     Tab 1
  *   </div>
  *   <div data-tab="tab1" data-tab-accordion-content>
  *     Tab Content
  *   </div>
  *  </div>
  *
  * 
  *  <div data-tab="tab2" data-tab-accordion>
  *   <div data-tab="tab2" data-tab-accordion-trigger>
  *     Tab 2
  *   </div>
  *   <div data-tab="tab2" data-tab-accordion-content>
  *     Tab Content
  *   </div>
  *  </div>
  * </div>
  */

/**
 * Tabs initialization
 * 
 * @return void
 */
flashCore.prototype.tabs = function(){
	// Hiding Tabs
	document.querySelectorAll('[data-tab]:not(.js_init--flashTabs)').forEach(function(node){
		node.style.display = 'none';
	});

	// Loading active Tabs
	document.querySelectorAll('[data-tab-target]:not(.js_init--flashTabs)').forEach(function(node) {
		if(node.classList.contains('active')) {
			document.querySelectorAll('[data-tab="' + node.getAttribute('data-tab-target') + '"]').forEach(function(tab){
				tab.style.display = '';
			});
			// Dispatching event to let other elements that tabs
			// are updated
			var flashReadyEvent = new CustomEvent('flashTabsUpdated');
			node.parentNode.parentNode.dispatchEvent(flashReadyEvent);
		}
	});

	// Loading active Tabs
	flash.listen(document.querySelectorAll('[data-tab-target]:not(.js_init--flashTabs)'), 'click', function(e){
		e.preventDefault();
		this.parentNode.parentNode.querySelectorAll('.active[data-tab-target]').forEach(function(trigger){
			trigger.classList.remove('active');
		});
		this.classList.add('active');
		flashTabsUpdate(this);
	});

	// Current tab on page load if url hash is not empty
	if(window.location.hash) {
		document.querySelectorAll('[data-tab-target="' + window.location.hash.replace('#', '') + '"]').forEach(function(trigger){
			trigger.click();
		});
	}

	// Marking elements as initialised
	document.querySelectorAll('[data-tab]:not(.js_init--flashTabs), [data-tab-target]:not(.js_init--flashTabs)').forEach(function(node){
		node.classList.add('js_init--flashTabs');
	});

	// Accordion on mobile
	flash.listen(document.body, 'click',  function(event){
		var tab = event.target.closest('[data-tab-accordion]');
		tab.classList.add('ignore_tab');
		document.querySelectorAll('[data-tab-accordion].open:not(.ignore_tab)').forEach(function(tab_to_close){
			tab_to_close.classList.remove('open');
			flash.slideToggle(tab_to_close.querySelector('[data-tab-accordion-content]'));
		});
		tab.classList.remove('ignore_tab');
		tab.classList.toggle('open');
		flash.slideToggle(tab.querySelector('[data-tab-accordion-content]'));
	}, '[data-tab-accordion-trigger]');


	/**
	 * Update specific tabs
	 * 
	 * @return void
	 */
	function flashTabsUpdate(clicked_tab) {
		var tab = document.querySelectorAll('[data-tab="' + clicked_tab.getAttribute('data-tab-target') + '"]');

		// This part of the code is for supporting multiple tab groups on the page
		// It searches for the parent of the group of tabs and then toggles just the tabs of this group
		var tab_group = clicked_tab.parentNode;
		// Searches for the parent of the group, so it goes up one level in the DOM
		// until it finds more than one [data-tab-target] element in the children of the
		// current object
		while(tab_group.querySelectorAll('[data-tab-target]').length == 1 && !tab_group.matches('body')) {
			tab_group = tab_group.parentNode;
		}
		// Toggles just the tabs in the current group
		tab_group.querySelectorAll('[data-tab-target]').forEach(function(el) {
			document.querySelectorAll('[data-tab="' + el.getAttribute('data-tab-target') + '"]').forEach( function(node){
				node.style.display = 'none';
				node.style.opacity = 0;
			});
		});
		tab_group.querySelectorAll('[data-tab-target]').forEach( function(node){
			node.classList.remove('active');
		});
		tab_group.querySelectorAll('[data-tab-target="' + clicked_tab.getAttribute('data-tab-target') + '"]').forEach( function(node){
			node.classList.add('active');
		});
		tab.forEach(function(node){
			flash.fadeIn(node, 0.1);
		});

		// Dispatching event to let other elements that tabs
		// are updated
		var flashReadyEvent = new CustomEvent('flashTabsUpdated');
		tab_group.dispatchEvent(flashReadyEvent);
	};
}

/* File: assets/js/flash/1.5.2/core/events/waitCondition.js */
/**
 * Wait for a specific condition to happen, then execute callback
 * @param  {function} 	condition   a function that returns the condition value
 * @param  {function} 	callback   	a callback to execute if the condition is true
 * @param  {int} 		timing   	time to wait between retries
 */
flashCore.prototype.waitCondition = function(condition, callback, timing, iterations) {
	var iterations 	= 0;
	var max_iterations 	= (10 || iterations);
	var timing 		= 20;

	function flashCheckCondition() {
		if(condition()) {
			callback();
		} else if(iterations < max_iterations) {
			iterations++;
			setTimeout(flashCheckCondition, timing);
		}
	}

	flashCheckCondition();
}

/* File: assets/js/flash/1.5.2/libraries/flashForm.js */
/**
 * v 1.0
 * Flash Forms Management
 * ---------------------------
 * 
 *
 * Add .js_form--no_flash to a form to make sure it's ignored by this script
 * or add .js_form--just_in_lightbox to make it work just in a lightbox
 */

var flash_current_form = null;

// Form Submission
function flashForm(element) {
    var self = this;
    // If an element is passed, the form is initialised
    if(element) {
        self.form = element;
        if(!self.form.classList.contains('js_init--flash-form')) {
            self.getSettings();
            self.initialise();
            self.fieldsEvents();
        }
    }
}

// Submissions handler url
flashForm.prototype.flash_submissions_handler = 'https://463z3kbl0f.execute-api.eu-west-1.amazonaws.com/default/flash-forms-handler-multipart';

// Check if submissions are enabled
flashForm.prototype.isEnabled = function() {
    var self = this;

    return self.settings.enabled;
}

// For initialisation
flashForm.prototype.initialise = function(){
    var self = this;

    // Add init class
    self.form.classList.add('js_init--flash-form');

    // JUST AJAX SUBMISSIONS
    if(self.settings.ajax_submit) {
        self.form.addEventListener('submit', function(event){
            // Disable Submit button
            var submit_button = self.form.querySelector('[type="submit"]');
            if(submit_button) {
                submit_button.disabled = true;
            }

            event.preventDefault();
            flash_current_form = self;
            self.preSubmissionActions();

            // Check if the form is enabled
            if(!self.isEnabled()) {
                self.afterSubmissionActions('error');
                return;
            }
            
            // Submission promises
            var submissions = [];

            // If the form has the cnf file
            // the script will submit also to lambda
            if(self.form.querySelectorAll('[name="cnf"]').length) {
                submissions.push(self.flashHandler());
            }
    
            // If the form has the action attribute
            // the script will submit also the the custom post url
            if(self.form.hasAttribute('action')) {
                submissions.push(self.otherHandler());
            }
            
            // Waiting for the requests to be completed
            Promise.all(submissions)
            .then(function ( data ) {
                self.afterSubmissionActions('success');
            })
            .catch(function(error){
                console.log(error)
                self.afterSubmissionActions('error', error);
            });
        });
    } else if(self.form.hasAttribute('action') && self.form.getAttribute('action') != '') {
        // NOT JUST AJAX SUBMISSIONS   
        // Add this class to prevent automatic posting to the action URL
        self.form.classList.add('js-prevent-submit');
        // Handling submission
        self.form.addEventListener('submit', function(event){
            flash_current_form = self;
            if(self.form.classList.contains('js-prevent-submit')) {
                self.preSubmissionActions();
                event.preventDefault();
                // Submission promises
                var submissions = [];

                // If the form has the cnf file
                // the script will submit also to lambda
                if(self.form.querySelectorAll('[name="cnf"]').length) {
                    submissions.push(self.flashHandler());
                }

                // Waiting for the requests to be completed
                Promise.all(submissions)
                .then(function ( data ) {
                    self.form.classList.remove('js-prevent-submit');
                    self.form.submit();
                })
                .catch(function(error){
                    self.afterSubmissionActions('error');
                });  
            }
        });
    }
}

// Events bound to fields
flashForm.prototype.fieldsEvents = function(){
    var self = this;

    // Filter Options Based on another field
    var options = self.form.querySelectorAll('option[data-filter]');
    if(options.length) {
        options.forEach(function() {
            var field = this;
            var filters = field.getAttribute('data-filter').split('|');
            var field = self.form.querySelector('[name="' + filters[0] + '"]');

            field.addEventListener('change', function(){
                if(field && (field.value == filters[1] || field.value == '' || !field.value)) {
                    field.style.display = '';
                } else {
                    field.style.display = 'none';
                }  
            });
        });
    }

    // Change settings based on a field
    var conditional_fields = self.form.querySelectorAll('[name="cnf"][data-conditional]')
    if(conditional_fields.length) {
        var listened_fields = [];
        conditional_fields.forEach(function(option){
            // Checking all the variants of the cnf
            if(listened_fields.indexOf(option.getAttribute('data-conditional')) == -1) {
                // Name of the field to watch for
                var field_name = option.getAttribute('data-conditional');
                var field = self.form.querySelector('[name="' + field_name + '"]');

                field.addEventListener('change', function(){
                    self.form.querySelectorAll('[name="cnf"]').forEach(function(field){
                        field.removeAttribute('checked');
                    });
                    
                    if(self.form.querySelector('[name="cnf"][data-conditional-value="' + field.value + '"]')) {
                        self.form.querySelector('[name="cnf"][data-conditional-value="' + field.value + '"]').setAttribute('checked', 'checked');
                    } else {
                        self.form.querySelector('[name="cnf"][data-conditional-default]').setAttribute('checked', 'checked');
                    }
                });

                listened_fields.push(option.getAttribute('data-conditional'));
            }
        });
    }
}

// Get form settings
flashForm.prototype.getSettings = function(){
    var self = this;
    
    // Initialise settings
    self.settings = {
        ajax_submit : true,
        enabled: true,
        prevent_thank_you_message: false
    };

    // Get the settings if they are there
    if(self.form.hasAttribute('data-flash-form')) {
        try {
            Object.assign(self.settings, JSON.parse('{' + self.form.getAttribute('data-flash-form') + '}'));
        } catch(error) {
            
        }
    }
}

// Pre submission actions
flashForm.prototype.preSubmissionActions = function(){
    var self = this;
    
    self.form.style.opacity = '0.6';

    // Custom Action
    if(self.settings.pre_submission_callback) {
        self.settings.enabled = eval(self.settings.pre_submission_callback);
    }
}

// After ajax submission events
flashForm.prototype.afterSubmissionActions = function(event, data){
    var self = this;

    switch (event) {
        case 'success':
            // Custom callback
            if(self.settings.success_callback) {
                eval(self.settings.success_callback);
            } else if(self.settings.thank_you_url) {
                // Redirect to Thank you Page
                window.location.href = self.settings.thank_you_url
            }
            if(!self.settings.prevent_thank_you_message && !self.settings.thank_you_url) {               
                // Show Thank You message
                self.form.style.opacity = 1;
                self.form.querySelector('.form__container').style.display = 'none';
                self.form.querySelector('.form__error').style.display = 'none';
                if(self.form.querySelector('.form__thank_you_message')) {
                    flash.fadeIn(self.form.querySelector('.form__thank_you_message'));
                }
            } 
            break;
        
        case 'error':
            if(self.settings.error_callback) {
                var form = self.form;
                eval(self.settings.error_callback);
            }
            self.form.style.opacity = 1;
            flash.fadeIn(self.form.querySelector('.form__error'));
            break;
    }

    // Disable Submit button
    var submit_button = self.form.querySelector('[type="submit"]');
    if(submit_button) {
        submit_button.removeAttribute('disabled');
    }
}

// Submission on flash lambda handler
flashForm.prototype.flashHandler = function(){
    var self = this;

    return (new Promise(function(main_resolve, main_reject){  
        // Get all the file inputs and loads them into an s3 bucket, then it'll pass
        // the url of the files to the email handler that will download the files
        // and it'll attach them to the email
        var submit_files_requests = [];
        self.form.querySelectorAll('[type="file"]').forEach(function(file_input) {
            if(file_input.files && file_input.files.length) {
                Object.keys(file_input.files).forEach(function(key) {
                    var file = file_input.files[key];
                    // Getting signatures and sending files
                    submit_files_requests.push(new Promise(function(resolve, reject){  
                        var request = new XMLHttpRequest();
                        request.open('POST', 'https://wvxig0amjb.execute-api.eu-west-1.amazonaws.com/production/forms-handler-uploads', true);
                        request.onload = function() {
                            if (this.status >= 200 && this.status < 400) {
                                // Got the signed url
                                var res_data = JSON.parse(request.responseText);
                                if(!res_data.url) {
                                    resolve();
                                }
        
                                // Uploads file  
                                var upload_request = new XMLHttpRequest();
                                upload_request.open('PUT', res_data.url, true);
                                upload_request.setRequestHeader('Content-Type', file.type);
                                upload_request.setRequestHeader('Content-Encoding', 'UTF-8');
                                upload_request.onload = function() {
                                    if (this.status >= 200 && this.status < 400) {
                                        var file_data = {};
                                        try {
                                            // If the file was uploaded, creates
                                            // an object to keep track of the field name
                                            // and the url of the uploaded file
                                            file_data = {
                                                field: 'AWS_attachment-' + file_input.name,
                                                name: file.name,
                                                url: res_data.url.split('?')[0]
                                            };
                                            file.value = '';
                                        } catch(e) {}
                                        resolve(file_data);
                                    } else {
                                        reject();
                                    }
                                }
                                upload_request.onerror = function(error) {
                                    reject();
                                };
                                upload_request.send(file);
                            } else {
                                reject();
                            }
                        };
                        request.onerror = function(error) {
                            reject('no');
                        };
                        // Sending just the filename to upload the file
                        request.send('file=' + file.name);
                    }));
                });
            }
        });

        // Waiting for all the files to be uploaded
        Promise.all(submit_files_requests)
        .then(function(submitted_files) {
            
            var handler_settings = {
                url: self.flash_submissions_handler,
                type: 'POST',
                data: ''
            }

            if(self.form.getAttribute('enctype') == 'multipart/form-data') {
                handler_settings.data = new FormData(self.form);
                // Get all the files and removes the file inputs from the form
                // appending text inputs with the url of the files uploaded in s3
                if(submitted_files.length) {
                    var files_to_send = {};
                    // Getting the groups of files submitted with the same field 
                    // (this is to support also multifile fields)
                    submitted_files.forEach(function(submitted_file){
                        if(!files_to_send[submitted_file.field]) {
                            files_to_send[submitted_file.field] = [];
                        }
                        files_to_send[submitted_file.field].push(submitted_file.url);
                    });
                    // Replace fields
                    Object.keys(files_to_send).forEach(function(field){
                        handler_settings.data.append(field, files_to_send[field].join(','));
                        handler_settings.data.delete(field.replace('AWS_attachment-', ''));
                    });
                    // Removing empty files
                    self.form.querySelectorAll('[type="file"]').forEach(function(file_input) {
                        if(!file_input.files || !file_input.files.length) {
                            handler_settings.data.delete(file_input.name);
                        }
                    });
                }
            } else {
                var cloned_form = self.form.cloneNode(true);
                cloned_form.querySelectorAll('input[type="text"],input[type="email"],input[type="checkbox"],select,textarea').forEach(function(el){
                    var initial_name = el.name;
                    if(cloned_form.querySelector('label[for="' + el.name + '"]')) {
                        el.name = cloned_form.querySelector('label[for="' + el.name + '"]').innerText;
                    } else if(el.placeholder) {
                        el.name = el.placeholder;
                    }
                    el.value = self.form.querySelector('[name="' + initial_name + '"]').value;
                });
                handler_settings.data = flash.serializeForm(cloned_form);
            }
            self.sendRequest(handler_settings, main_resolve, main_reject);
        })
        .catch(function(error){
            self.afterSubmissionActions('error');
        });
    }));
}

// Submission on custom handler
flashForm.prototype.otherHandler = function(){
    var self = this;

    return (new Promise(function(resolve, reject){  
        var handler_settings = {
            url: self.form.getAttribute('action'),
            type: "POST",
            data: ''
        };

        if(self.form.getAttribute('enctype') == 'multipart/form-data') {
            handler_settings.data = new FormData(self.form);
            handler_settings.cache = false;
            handler_settings.contentType = false;
            handler_settings.processData = false;
        } else {
            handler_settings.data = flash.serializeForm(self.form);
        }

        // Jsonp option in case of CORS not working with normal requests
        if(self.settings.enable_jsonp) {
            handler_settings.dataType = "jsonp";
            handler_settings.crossDomain = true;
        }

        self.sendRequest(handler_settings, resolve, reject);
    }));
}

flashForm.prototype.sendRequest = function(handler_settings, resolve, reject){
    var self = this;

    var request = new XMLHttpRequest();
    request.open('POST', handler_settings.url, true);

    request.onload = function() {
        if (this.status >= 200 && this.status < 400) {
            resolve('yay');
        } else {
            reject(this.responseText);
        }
    };

    request.onerror = function(error) {
        reject(error);
    };
    
    request.send(handler_settings.data);
}

// Callback for jsonp requests
function formCallback(data) {
    if(data.submission_status) {
        self.afterSubmissionActions('success');
    } else {
        self.afterSubmissionActions('error');
    }
}

// Init function
function initFlashForms() {
    document.querySelectorAll('form:not(.js_form--no_flash):not(.js_form--just_in_lightbox), .js-lightbox form.js_form--just_in_lightbox').forEach(function(form){
        var flash_form = new flashForm(form);
    });
}

// Init
flash.ready(function(){
    initFlashForms();
});

/* File: blocks/core/lp_header/script.js */
flash.ready(function(block){

	// Follow nav script
	var follow_enabled 		= false;
	var scroll_down_limit 	= 0;
    var scroll_up_limit 	= 0;
    var scroll_limit        = 0;
	var header 				= block;
	var menu 				= block.querySelector('.core_lp_header__top');

	function setupFollowNav() {
		if(document.querySelector('.core_header')) {
			var mainHeader = document.querySelector('.core_header');
			scroll_limit = mainHeader.offsetTop + mainHeader.offsetHeight;
		} else {
			scroll_limit = block.offsetTop + menu.offsetHeight;
		}
	}

	function followNav() {
		var scroll_top = window.pageYOffset;

		if(scroll_top >= scroll_limit && !follow_enabled) {
			enableFollowNav();
		} else if(scroll_top <= scroll_limit && follow_enabled) {
			disableFollowNav();
		}
	}

	function enableFollowNav() {
		follow_enabled = true;

		// If it's not already fixed
		if(!header.classList.contains('core_lp_header--fixed')) {
			menu.classList.add('start');
			header.classList.add('core_lp_header--fixed');

			setTimeout(function(){
				menu.classList.remove('start');
			}, 10);	
		}
	}

	function disableFollowNav() {
		follow_enabled = false;

		menu.classList.add('gone');

		setTimeout(function(){
			menu.classList.remove('gone');
			header.classList.remove('core_lp_header--fixed');
		}, 10);
	}

	// Follow nav binding
	setupFollowNav();
	window.addEventListener('scroll', followNav);
	window.addEventListener('resize', setupFollowNav);
	followNav();

}, 'core_lp_header');

/* File: blocks/core/header/script.js */
flash.ready(function(block){

	// Follow nav script
	var follow_enabled 		= false;
	var scroll_down_limit 	= 0;
    var scroll_up_limit 	= 0;
    var scroll_limit        = 0;
	var header 				= block;
	var menu 				= block.querySelector('.core_header__top');

	function setupFollowNav() {
		scroll_limit = block.offsetTop + menu.offsetHeight;
	}

	function followNav() {
		var scroll_top = window.pageYOffset;

		if(scroll_top >= scroll_limit && !follow_enabled) {
			enableFollowNav();
		} else if(scroll_top <= scroll_limit && follow_enabled) {
			disableFollowNav();
		}
	}

	function enableFollowNav() {
		follow_enabled = true;

		// If it's not already fixed
		if(!header.classList.contains('core_header--fixed')) {
			menu.classList.add('start');
			header.classList.add('core_header--fixed');

			setTimeout(function(){
				menu.classList.remove('start');
			}, 10);	
		}
	}

	function disableFollowNav() {
		follow_enabled = false;

		menu.classList.add('gone');

		setTimeout(function(){
			menu.classList.remove('gone');
			header.classList.remove('core_header--fixed');
		}, 10);
	}

	// Follow nav binding
	setupFollowNav();
	window.addEventListener('scroll', followNav);
	window.addEventListener('resize', setupFollowNav);
	followNav();

}, 'core_header');

/* File: blocks/template/resources_v2/script.js */
// flash.ready(function(block) {
//   function updateListing () {
//     var category = block.querySelector('.template_resources_v2__categories a.active').getAttribute('data-resource-category-target')
//     block.querySelectorAll('.template_resources_v2__resources > a').forEach(function(el) { el.classList.remove('hidden') })
//     if (category !== 'All Resources') {
//       block.querySelectorAll('.template_resources_v2__resources > a').forEach(function(el) {
//         if (el.getAttribute('data-resource-category') !== category) el.classList.add('hidden')
//       })
//     }
//   }

//   flash.listen('.template_resources_v2__categories a', 'click', function(e) {
//     e.preventDefault()
//     block.querySelectorAll('.template_resources_v2__categories a').forEach(function(el) { el.classList.remove('active') })
//     this.classList.add('active')
//     updateListing()
//   });
// }, 'template_resources_v2')

/* File: blocks/block/top_cta_v2/script.js */
flash.ready(function(block){
  flash.listen('.block_top_cta_v2__close', 'click', function(){
      block.style.display = 'none';
      flash.createCookie('hide_cta', true, 1);
  });
}, 'block_top_cta_v2')

/* File: blocks/block/top_cta/script.js */
flash.ready(function(block){
    flash.listen('.block_top_cta__close', 'click', function(){
        block.style.display = 'none';
        flash.createCookie('hide_cta', true, 1);
    });
}, 'block_top_cta')

/* File: blocks/block/testimonials_slider/script.js */
flash.ready(function(block) {
    var slider = block.querySelector('.splide')
    if (slider) new Splide(slider, {
        type: 'loop',
        fixedWidth: '668px',
        gap: '85px',
        focus: 'center',
        padding: {
            right: '5rem',
            left : '5rem',
        },
        arrows: false,
        classes: {
            pagination: 'unstyled splide__bullets'
        },
        autoplay: true,
        interval: 7000,
        breakpoints: {
            '940': {
                fixedWidth: 0,
                focus: 0,
                padding: {
                    right: 0,
                    left : 0,
                },
            }
        }
    }).mount();
}, 'block_testimonials_slider');

/* File: blocks/block/resources_detail/script.js */
flash.ready(function (block) {
  var follow_enabled = false;

  function followNav() {
    var scroll_top = window.pageYOffset;
    if (scroll_top >= 200 && !follow_enabled) {
      follow_enabled = true;
      block.classList.add("active");
    } else if (scroll_top <= 200 && follow_enabled) {
      follow_enabled = false;
      block.classList.remove("active");
    }
  }

  window.addEventListener("scroll", followNav);
  window.addEventListener("resize", followNav);
  followNav();
}, "block_creative_courses_detail__follow_navigation");


/* File: blocks/block/related_courses/script.js */
flash.ready(function(block) {

  // Expand
	flash.listen(block.querySelector('.block_related_courses__expand a'), 'click', function(e) {
		e.preventDefault();
    block.querySelectorAll('div.block_related_courses__grid ul.why_ucd__points').forEach(function(el) {
      el.style.display = 'block';
    });
    block.querySelector('div.block_related_courses__expand').style.display = 'none';
    block.querySelectorAll('a.course-box-expand').forEach(function(el) {
      el.style.display = 'none';
    });
    block.querySelector('.block_related_courses__collapse').style.display = 'block';
	});

  // Collapse (in a rush atm, no time to code clean)
  flash.listen(block.querySelector('.block_related_courses__collapse a'), 'click', function(e) {
		e.preventDefault();
    block.querySelectorAll('div.block_related_courses__grid ul.why_ucd__points').forEach(function(el) {
      el.style.display = 'none';
    });
    block.querySelector('div.block_related_courses__collapse').style.display = 'none';
    block.querySelectorAll('a.course-box-expand').forEach(function(el) {
      el.style.display = 'none';
    });
    block.querySelector('.block_related_courses__expand').style.display = 'block';
	});

  // Expand mobile
  flash.listen(block.querySelectorAll('a.course-box-expand'), 'click', function(e, el) {
		e.preventDefault();
    e.target.parentNode.querySelector('ul.why_ucd__points').style.display = 'block';
    block.querySelector('div.block_related_courses__expand').style.display = 'none';
    e.target.parentNode.querySelector('a.course-box-expand').style.display = 'none';
    // If they want collapse
    // e.target.parentNode.querySelector('a.course-box-collapse').style.display = 'block';
  });

  // Just in case if they want collapse
  // flash.listen(block.querySelectorAll('a.course-box-collapse'), 'click', function(e, el) {
	// 	e.preventDefault();
  //   e.target.parentNode.querySelector('ul.why_ucd__points').style.display = 'none';
  //   block.querySelector('div.block_related_courses__expand').style.display = 'none';
  //   e.target.parentNode.querySelector('a.course-box-expand').style.display = 'block';
  //   e.target.parentNode.querySelector('a.course-box-collapse').style.display = 'none';
  // });
}, 'block_related_courses');

/* File: blocks/block/logos_alternative/script.js */
flash.ready(function(block){
	var logos_number = document.querySelectorAll('.block_logos_alternative__logo_wrapper:nth-child(1) .block_logos_alternative__logo').length;
	var last_position = '';

	if(logos_number > 6) {
		var timestamp = (new Date()).getTime();
		block.setAttribute('data-time', timestamp);
		function changeLogos() {
			if(!document.querySelector('[data-time="' + timestamp + '"]')) {
				return;
			}
			// Getting new position
			var position = null;
			var max = 0;
			do {
				position = getRandomInt(6) + 1;
				max++;
			} while(last_position === position && max < 50);

			last_position = position;
			
			var new_logo_index = null;
			do {
				new_logo_index = getRandomInt(logos_number);
				max++;
			} while (document.querySelector('[data-time="' + timestamp + '"] .visible[data-index="' + new_logo_index + '"]') && max < 50);

			// Appending the logo
			var new_logo = document.querySelector('[data-time="' + timestamp + '"] .block_logos_alternative__logo_wrapper:nth-child(' + position + ') [data-index="' + new_logo_index + '"]');
			var visible_logo = document.querySelector('[data-time="' + timestamp + '"] .block_logos_alternative__logo_wrapper:nth-child(' + position + ') .visible');
			if(!new_logo || !visible_logo) {
				return;
			}
			new_logo.classList.add('visible');
			visible_logo.classList.add('disappearing');
			if(!enable_slider) {
				return;
			}
			logos_timeout = setTimeout(function(){
				visible_logo.classList.remove('visible');
				visible_logo.classList.remove('disappearing');
				setTimeout(changeLogos, 100);
			}, 800);			
		}
		
		var logos_timeout = setTimeout(changeLogos, 2500);
		var enable_slider = true;

		function getRandomInt(max) {
	  		return Math.floor(Math.random() * Math.floor(max));
		}
	}
}, 'block_logos_alternative');

/* File: blocks/block/logos/script.js */
flash.ready(function(block){
	var logos_number = document.querySelectorAll('.block_logos__logo_wrapper:nth-child(1) .block_logos__logo').length;
	var last_position = '';

	if(logos_number > 6) {
		var timestamp = (new Date()).getTime();
		block.setAttribute('data-time', timestamp);
		function changeLogos() {
			if(!document.querySelector('[data-time="' + timestamp + '"]')) {
				return;
			}
			// Getting new position
			var position = null;
			var max = 0;
			do {
				position = getRandomInt(6) + 1;
				max++;
			} while(last_position === position && max < 50);

			last_position = position;
			
			var new_logo_index = null;
			do {
				new_logo_index = getRandomInt(logos_number);
				max++;
			} while (document.querySelector('[data-time="' + timestamp + '"] .visible[data-index="' + new_logo_index + '"]') && max < 50);

			// Appending the logo
			var new_logo = document.querySelector('[data-time="' + timestamp + '"] .block_logos__logo_wrapper:nth-child(' + position + ') [data-index="' + new_logo_index + '"]');
			var visible_logo = document.querySelector('[data-time="' + timestamp + '"] .block_logos__logo_wrapper:nth-child(' + position + ') .visible');
			if(!new_logo || !visible_logo) {
				return;
			}
			new_logo.classList.add('visible');
			visible_logo.classList.add('disappearing');
			if(!enable_slider) {
				return;
			}
			logos_timeout = setTimeout(function(){
				visible_logo.classList.remove('visible');
				visible_logo.classList.remove('disappearing');
				setTimeout(changeLogos, 100);
			}, 800);			
		}
		
		var logos_timeout = setTimeout(changeLogos, 2500);
		var enable_slider = true;

		function getRandomInt(max) {
	  		return Math.floor(Math.random() * Math.floor(max));
		}
	}
}, 'block_logos');

/* File: blocks/block/hubspot_form/script.js */
/**
 * This function handles custom errors from Hubspot validation coming from the 
 * Lambda function 'hubspot-form-post' in the WT4 account
 */
function hubspotErrors(data, form) {
    var errors_data = JSON.parse(data);
    var hs_error_message = '';
    if (errors_data.errors && errors_data.errors.length) {
        errors_data.errors.forEach(function(el) {
            if(el.message === "Error in 'fields.email'. Submission from this email address are not allowed") {
                hs_error_message += 'Please use a business email address.<br/>';
            }
        });

    }
    form.querySelector('.form__error').innerHTML = hs_error_message || 'Error while submitting the form. Please try again.';
}

/* File: blocks/block/faq/script.js */
flash.ready(function(block){
	// Toggle a FAQ
	flash.listen(block.querySelectorAll('.faq_item__question'), 'click', function(event){
		var question = this;
		if(question.dataset.animating) {
			return;
		}
		question.dataset.animating = 'true';

		question.parentNode.classList.toggle('faq_item--open');
		flash.slideToggle(question.parentNode.querySelector('.faq_item__answer_wrapper'), 200, function(){
			question.removeAttribute('data-animating');
		});
	});

	// Open first question
	setTimeout(function(){
		var firstQ = block.querySelector('.faq_item');
		firstQ.classList.add('faq_item--open');
		flash.slideDown(firstQ.parentNode.querySelector('.faq_item__answer_wrapper'), 200);
	}, 2000)
}, 'block_faqs');

/* File: blocks/block/courses_listing/script.js */
flash.ready(function(block) {
  var button = document.querySelector('a.block_courses_listing__to_top')
  window.addEventListener('scroll', function (event) {
    if (!button) return null
    var scroll = this.scrollY;
    if (scroll >= 540) button.classList.add('active')
    else button.classList.remove('active')
  })

  // Element.prototype.fadeIn = function () {
  //   var cls = this.classList.contains('visible') ? 'visible-alt' : 'visible'
  //   this.classList.remove('visible')
  //   this.classList.remove('visible-alt')
  //   this.classList.remove('hidden')
  //   this.classList.add(cls)
  // }

  // Element.prototype.hide = function () {
  //   this.classList.remove('visible')
  //   this.classList.remove('visible-alt')
  //   this.classList.add('hidden')
  // }

  // function setSubject (subject) {
  //   var queryParams = new URLSearchParams(window.location.search)
  //   block.querySelectorAll('[data-subject-target]').forEach(function (el) {
  //     el.classList.remove('active')
  //     if (el.getAttribute('data-subject-target') === subject) el.classList.add('active')
  //   })
  //   if (subject === 'All') {
  //     block.querySelectorAll('[data-subject]').forEach(function (el) { el.fadeIn() })
  //     queryParams.delete('subject')
  //     // window.history.replaceState({ url: window.location.href }, null, window.location.pathname)
  //   } else {
  //     block.querySelectorAll('[data-subject]').forEach(function (el) {
  //       if (el.getAttribute('data-subject') === subject) {
  //         el.fadeIn()
  //       } else {
  //         el.hide()
  //       }
  //     })
  //     queryParams.set('subject', subject)
  //   }
  //   var hasQueryParams = queryParams.toString() ? true : false
  //   window.history.replaceState({ url: window.location.href }, null, window.location.pathname + (hasQueryParams ? '?' + queryParams.toString() : ''))
  // }

  // var subject = flash.getUrlParameter('subject') || 'All'
  // setSubject(subject)

  // flash.listen(block.querySelectorAll('a.scroll'), 'click', function(e) {
  //   block.querySelectorAll('[data-subject-target]').forEach(function (el) {
  //     el.classList.remove('active')
  //     if (el.getAttribute('data-subject-target') === 'All') el.classList.add('active')
  //   })
  //   block.querySelectorAll('[data-subject]').forEach(function (el) {
  //     el.fadeIn()
  //   })
  // })

  // flash.listen(block.querySelectorAll('[data-subject-target]'), 'click', function(e) {
	// 	e.preventDefault()
  //   var subject = this.getAttribute('data-subject-target')
  //   setSubject(subject)
	// })
}, 'block_courses_listing')

/* File: blocks/block/creative_courses_detail/script.js */
flash.ready(function (block) {
  var follow_enabled = false;

  function followNav() {
    var scroll_top = window.pageYOffset;
    if (scroll_top >= 200 && !follow_enabled) {
      follow_enabled = true;
      block.classList.add("active");
    } else if (scroll_top <= 200 && follow_enabled) {
      follow_enabled = false;
      block.classList.remove("active");
    }
  }

  window.addEventListener("scroll", followNav);
  window.addEventListener("resize", followNav);
  followNav();
}, "block_creative_courses_detail__follow_navigation");


/* File: blocks/block/courses_detail_variants/script.js */
flash.ready(function (block) {
    // V2
    // Open / Close Variant on Click
    var active = document.querySelector('.block_courses_detail_variants_v2__variant.active')
    if (!active) {
        var fallback = document.querySelector('.block_courses_detail_variants_v2__variant:first-of-type')
        if (fallback) fallback.classList.add('active')
    }
    document.querySelectorAll('.block_courses_detail_variants_v2__box_header').forEach(function (el) {
        el.addEventListener('click', function (e) {
            e.preventDefault()
            if (el.parentNode.classList.contains('active')) {
                return el.parentNode.classList.remove('active')
            }
            active = document.querySelector('.block_courses_detail_variants_v2__variant.active')
            if (active) active.classList.remove('active')
            el.parentNode.classList.add('active')
        })
    })

    // Show More
    var showMore = document.querySelector('a.block_courses_detail_variants_v2__show_more')
    if (showMore) {
        showMore.addEventListener('click', function (e) {
            e.preventDefault()
            document.querySelectorAll('.block_courses_detail_variants_v2__variant.hidden').forEach(function (el) { el.classList.remove('hidden') })
            document.querySelector('a.block_courses_detail_variants_v2__show_more').style.display = 'none'
        })
    }

    // Filters
    var select = document.querySelector('.block_courses_detail_variants_v2__filter select')
    if (select) {
        select.addEventListener('change', function (e) {
            e.preventDefault()
            if (showMore) showMore.style.display = 'block'
            var variants = document.querySelectorAll('.block_courses_detail_variants_v2__variant:not(.featured)')
            variants.forEach(function (el) { el.classList.add('hidden') })
            var month = e.target.value
            if (month.indexOf('-') >= 0) { // All
                variants.forEach(function (el, index) {
                    if (index <= 4) el.classList.remove('hidden')
                })
                return
            }
            document.querySelector('[data-filter-label]').textContent = month
            document.querySelectorAll('.block_courses_detail_variants_v2__variant[data-month="' + month.split(' ')[0] + '"]').forEach(function (el, index) {
                el.classList.remove('hidden')
            })
            if (showMore) showMore.style.display = 'none'
        })
    }
}, 'block_courses_detail_variants_v2');

/* File: blocks/block/courses_detail_overview_1/script.js */
flash.ready(function(block){
    flash.listen('[data-show-modules],[data-hide-modules]', 'click', function(){
        block.querySelector('.block_course_detail_overview_1__modules_wrapper').classList.toggle('block_course_detail_overview_1__modules_wrapper--open');
    });
}, 'block_course_detail_overview_1');

/* File: blocks/block/courses_detail_overview_2/script.js */
flash.ready(function(block){
    flash.listen('[data-show-modules],[data-hide-modules]', 'click', function(){
        block.querySelector('.block_course_detail_overview_2__modules_wrapper').classList.toggle('block_course_detail_overview_2__modules_wrapper--open');
    });
}, 'block_course_detail_overview_2');

/* File: blocks/block/courses_detail_on_demand_variants/script.js */
flash.ready(function (block) {

    // V2
    // Open / Close Variant on Click
    var active = document.querySelector('.block_courses_detail_on_demand_variants__variant.active')
    if (!active) document.querySelector('.block_courses_detail_on_demand_variants__variant:first-of-type').classList.add('active')
    document.querySelectorAll('.block_courses_detail_on_demand_variants__box_header').forEach(function (el) {
        el.addEventListener('click', function (e) {
            e.preventDefault()
            if (el.parentNode.classList.contains('active')) {
                return el.parentNode.classList.remove('active')
            }
            active = document.querySelector('.block_courses_detail_on_demand_variants__variant.active')
            if (active) active.classList.remove('active')
            el.parentNode.classList.add('active')
        })
    })

    // Show More
    var showMore = document.querySelector('a.block_courses_detail_on_demand_variants__show_more')
    if (showMore) {
        showMore.addEventListener('click', function (e) {
            e.preventDefault()
            document.querySelectorAll('.block_courses_detail_on_demand_variants__variant.hidden').forEach(function (el) { el.classList.remove('hidden') })
            document.querySelector('a.block_courses_detail_on_demand_variants__show_more').style.display = 'none'
        })
    }

    // Filters
    var select = document.querySelector('.block_courses_detail_on_demand_variants__filter select')
    if (select) {
        select.addEventListener('change', function (e) {
            e.preventDefault()
            if (showMore) showMore.style.display = 'block'
            var variants = document.querySelectorAll('.block_courses_detail_on_demand_variants__variant:not(.featured)')
            variants.forEach(function (el) { el.classList.add('hidden') })
            var month = e.target.value
            if (month.indexOf('-') >= 0) { // All
                variants.forEach(function (el, index) {
                    if (index <= 4) el.classList.remove('hidden')
                })
                return
            }
            document.querySelector('[data-filter-label]').textContent = month
            document.querySelectorAll('.block_courses_detail_on_demand_variants__variant[data-month="' + month.split(' ')[0] + '"]').forEach(function (el, index) {
                el.classList.remove('hidden')
            })
            if (showMore) showMore.style.display = 'none'
        })
    }
}, 'block_courses_detail_on_demand_variants');

/* File: blocks/block/courses_detail_intro/script.js */
flash.ready(function(block){
    setTimeout(function(){
        flash.fadeIn(block, 1000);
    }, 3000);

    flash.listen('.intro_popup__close', 'click', function(){
        flash.fadeOut(block, 300);
    });
}, 'intro_popup');

/* File: blocks/block/course_modules/script.js */
flash.ready(function (block) {

  // Open / Close Module on Click
  var active = document.querySelector('.block_course_modules__module.active')
  if (!active) document.querySelector('.block_course_modules__module:first-of-type').classList.add('active')
  document.querySelectorAll('.block_course_modules__module_header').forEach(function (el) {
      el.addEventListener('click', function (e) {
          e.preventDefault()
          if (el.parentNode.classList.contains('active')) {
              return el.parentNode.classList.remove('active')
          }
          active = document.querySelector('.block_course_modules__module.active')
          if (active) active.classList.remove('active')
          el.parentNode.classList.add('active')
      })
  })

}, 'block_course_modules');

/* File: blocks/creative/testimonials/script.js */
flash.ready(function(block) {
  var slider = block.querySelector('.splide')
  if (slider) new Splide(slider, {
      type: 'loop',
      fixedWidth: '415px',
      gap: '25px',
      focus: 'center',
      padding: {
          right: '5rem',
          left : '5rem',
      },
      arrows: true,
      arrowPath: 'M0.872849 6.56374L7.15854 0.277917C7.33796 0.098488 7.57711 0 7.8321 0C8.08738 0 8.32638 0.0986295 8.50581 0.277917L9.0765 0.848751C9.25579 1.0279 9.35456 1.26718 9.35456 1.52232C9.35456 1.77731 9.25579 2.02466 9.0765 2.20381L5.40952 5.87886H17.0597C17.585 5.87886 18 6.29007 18 6.81548V7.62249C18 8.1479 17.585 8.60058 17.0597 8.60058H5.36792L9.07636 12.2961C9.25565 12.4756 9.35442 12.7083 9.35442 12.9635C9.35442 13.2183 9.25565 13.4545 9.07636 13.6338L8.50567 14.2028C8.32624 14.3822 8.08724 14.48 7.83196 14.48C7.57697 14.48 7.33782 14.3809 7.15839 14.2015L0.872707 7.91583C0.692854 7.73584 0.593941 7.49556 0.594648 7.24014C0.594082 6.98387 0.692854 6.74346 0.872849 6.56374Z',
      classes: {
          pagination: 'unstyled splide__bullets'
      },
      autoplay: true,
      interval: 7000,
      breakpoints: {
          '940': {
              fixedWidth: 0,
              focus: 'center',
              gap: '20px',
              padding: {
                  right: '2.2rem',
                  left : '2.2rem',
              },
          }
      }
  }).mount();
}, 'creative_testimonials');

/* File: blocks/creative/skill_course_levels/script.js */
flash.ready(function (block) {
  const container = block;
  // ✅ Simulate click on Level 1 after 1 second
  setTimeout(() => {
    const firstTabBtn = container.querySelector('.tab-button[data-tab-index="0"]');
    if (firstTabBtn) {
      firstTabBtn.click();
    }
  }, 1); // 1ms

  // Tab switching
  container.querySelectorAll(".tab-button").forEach((btn) => {
    btn.addEventListener("click", function () {
      const tabIndex = this.dataset.tabIndex;

      container.querySelectorAll(".tab-button").forEach((b) => {
        b.classList.remove("bg-green-300", "text-purple-900");
        b.classList.add("bg-green-300/30", "text-purple-900/60");
      });
      this.classList.remove("bg-green-300/30", "text-purple-900/60");
      this.classList.add("bg-green-300", "text-purple-900");

      container.querySelectorAll(".tab-content").forEach((tab) => {
        tab.classList.add("hidden");
        tab.style.display = "none";
      });

      const activeTab = container.querySelector(`.tab-content[data-tab="${tabIndex}"]`);
      if (activeTab) {
        activeTab.classList.remove("hidden");
        activeTab.style.display = "block";
      }
    });
  });

  // Accordion toggling
  container.querySelectorAll(".accordion-toggle").forEach((btn) => {
    btn.addEventListener("click", function () {
      const content = btn.nextElementSibling;
      const icon = btn.querySelector("svg");
      const isOpen = !content.classList.contains("hidden");

      container.querySelectorAll(".accordion-content").forEach((el) => el.classList.add("hidden"));
      container.querySelectorAll(".accordion-toggle svg").forEach((i) => i.classList.remove("rotate-180"));

      if (!isOpen) {
        content.classList.remove("hidden");
        icon.classList.add("rotate-180");
      }
    });
  });
}, "skill_course_levels");


/* File: blocks/creative/popular_courses/script.js */
flash.ready(function (block) {
  // V2
  // Open / Close Variant on Click
  var active = block.querySelector('.creative_popular_courses__category.active')
  if (!active) {
    active = block.querySelector('.creative_popular_courses__category:first-of-type')
    active.classList.add('active')
  }

  function filterCourses (category) {
    var courses = block.querySelectorAll('[data-categories]')
    if (courses) courses.forEach(function (course) {
      if (course.getAttribute('data-categories').toLowerCase().indexOf(category.toLowerCase()) >= 0) course.style.display = 'flex'
      else course.style.display = 'none'
    })
  }

  filterCourses(active.getAttribute('data-category-target'))

  block.querySelectorAll('.creative_popular_courses__category').forEach(function (el) {
    el.addEventListener('click', function (e) {
      e.preventDefault()
      var active = block.querySelector('.creative_popular_courses__category.active')
      if (active) active.classList.remove('active')
      el.classList.add('active')
      var category = el.getAttribute('data-category-target')
      filterCourses(category)
    })
  })

  // Responsive Dropdown
  var select = document.querySelector('.creative_main_courses_listing__select select')
  if (select) {
    select.addEventListener('change', function (e) {
      e.preventDefault()
      var category = e.target.value
      filterCourses(category)
    })
  }
}, 'creative_popular_courses');

/* File: blocks/creative/logo_grid/script.js */
flash.ready(function(block){
	var logos_number = document.querySelectorAll('.creative_logo_grid__logo_wrapper:nth-child(1) .creative_logo_grid__logo').length;
	var last_position = '';
  var limit = flash.compareBreakpoint('medium', '<=') ? 4 : 5
  var duration = flash.compareBreakpoint('medium', '<=') ? 1000 : 800

	if(logos_number > limit) {
		var timestamp = (new Date()).getTime();
		block.setAttribute('data-time', timestamp);
		function changeLogos() {
			if(!document.querySelector('[data-time="' + timestamp + '"]')) {
				return;
			}
			// Getting new position
			var position = null;
			var max = 0;
			do {
				position = getRandomInt(limit) + 1;
				max++;
			} while(last_position === position && max < 50);

			last_position = position;
			
			var new_logo_index = null;
			do {
				new_logo_index = getRandomInt(logos_number);
				max++;
			} while (document.querySelector('[data-time="' + timestamp + '"] .visible[data-index="' + new_logo_index + '"]') && max < 50);

			// Appending the logo
			var new_logo = document.querySelector('[data-time="' + timestamp + '"] .creative_logo_grid__logo_wrapper:nth-child(' + position + ') [data-index="' + new_logo_index + '"]');
			var visible_logo = document.querySelector('[data-time="' + timestamp + '"] .creative_logo_grid__logo_wrapper:nth-child(' + position + ') .visible');
			if(!new_logo || !visible_logo) {
				return;
			}
			new_logo.classList.add('visible');
			visible_logo.classList.add('disappearing');
			if(!enable_slider) {
				return;
			}
			logos_timeout = setTimeout(function(){
				visible_logo.classList.remove('visible');
				visible_logo.classList.remove('disappearing');
				setTimeout(changeLogos, 100);
			}, duration);			
		}
		
		var logos_timeout = setTimeout(changeLogos, 2500);
		var enable_slider = true;

		function getRandomInt(max) {
	  		return Math.floor(Math.random() * Math.floor(max));
		}
	}
}, 'creative_logo_grid');

/* File: blocks/creative/main_courses_listing/script.js */
flash.ready(function (block) {

  // Look for the active course
  setTimeout(function() {
    var active = block.querySelector('.creative_main_courses_listing__sidebar__desktop .active')
    var select = block.querySelector('.creative_main_courses_listing__select select')
    if (active && select) {
      select.value = active.getAttribute('href')
    }
    select.addEventListener('change', function(e) {
      window.location.href = e.target.value
    })
  }, 1000)

}, 'creative_main_courses_listing');

/* File: blocks/creative/hero_banner/script.js */
flash.ready(function(block){

	$(document).on('change', 'input.hs-input[type="file"]', function(e) {
		var file = e.target.files[0];
		var name = file.name;
		console.log($(this), $(this).parent().parent(), $(this).parent().parent().find('label'))
		var $p = $(this).parent().parent().find('label p')
		if ($p) $p.remove()
		$(this).parent().parent().find('label').append('<p>' + name +'</p>');
	})

	var logos_number = document.querySelectorAll('.creative_hero_banner__logo_wrapper:nth-child(1) .creative_hero_banner__logo').length;
	var last_position = '';
  var limit = 4
  var duration = flash.compareBreakpoint('medium', '<=') ? 1000 : 800

	if(logos_number > limit) {
		var timestamp = (new Date()).getTime();
		block.setAttribute('data-time', timestamp);
		function changeLogos() {
			if(!document.querySelector('[data-time="' + timestamp + '"]')) {
				return;
			}
			// Getting new position
			var position = null;
			var max = 0;
			do {
				position = getRandomInt(limit) + 1;
				max++;
			} while(last_position === position && max < 50);

			last_position = position;
			
			var new_logo_index = null;
			do {
				new_logo_index = getRandomInt(logos_number);
				max++;
			} while (document.querySelector('[data-time="' + timestamp + '"] .visible[data-index="' + new_logo_index + '"]') && max < 50);

			// Appending the logo
			var new_logo = document.querySelector('[data-time="' + timestamp + '"] .creative_hero_banner__logo_wrapper:nth-child(' + position + ') [data-index="' + new_logo_index + '"]');
			var visible_logo = document.querySelector('[data-time="' + timestamp + '"] .creative_hero_banner__logo_wrapper:nth-child(' + position + ') .visible');
			if(!new_logo || !visible_logo) {
				return;
			}
			new_logo.classList.add('visible');
			visible_logo.classList.add('disappearing');
			if(!enable_slider) {
				return;
			}
			logos_timeout = setTimeout(function(){
				visible_logo.classList.remove('visible');
				visible_logo.classList.remove('disappearing');
				setTimeout(changeLogos, 100);
			}, duration);			
		}
		
		var logos_timeout = setTimeout(changeLogos, 2500);
		var enable_slider = true;

		function getRandomInt(max) {
	  		return Math.floor(Math.random() * Math.floor(max));
		}
	}
}, 'creative_hero_banner');

/* File: blocks/creative/faq/script.js */
flash.ready(function (block) {
  // Open / Close Module on Click
  var active = document.querySelector(".creative_faq__faq.active");
  if (!active) document.querySelector(".creative_faq__faq:first-of-type").classList.add("active");
  document.querySelectorAll(".creative_faq__faq h3").forEach(function (el) {
    el.addEventListener("click", function (e) {
      e.preventDefault();
      if (el.parentNode.classList.contains("active")) {
        return el.parentNode.classList.remove("active");
      }
      active = document.querySelector(".creative_faq__faq.active");
      if (active) active.classList.remove("active");
      el.parentNode.classList.add("active");
    });
  });
}, "creative_faq");


/* File: blocks/creative/courses_charts/script.js */
flash.ready(function (block) {
  var otherTab = block.querySelector(".creative_courses_charts__other");
  block.querySelectorAll("[data-group-target]").forEach(function (el) {
    el.addEventListener("click", function (e) {
      e.preventDefault();
      if (otherTab) {
        var closestOtherTab = el.closest(".creative_courses_charts__other");
        if (closestOtherTab && closestOtherTab.isEqualNode(otherTab)) {
          otherTab.classList.add("active");
        } else {
          otherTab.classList.remove("active");
        }
      }
      block.querySelectorAll("[data-group-target]").forEach(function (el) {
        el.classList.remove("active");
      });
      block.querySelectorAll("[data-group]").forEach(function (el) {
        el.classList.remove("active");
      });
      el.classList.add("active");
      block.querySelectorAll('[data-group="' + el.getAttribute("data-group-target") + '"]').forEach(function (el) {
        el.classList.add("active");
      });
    });
  });
}, "creative_courses_charts");


/* File: blocks/creative/course_modules/script.js */
flash.ready(function (block) {

  // Open / Close Module on Click
  var active = document.querySelector('.creative_course_modules__module.active')
  if (!active) document.querySelector('.creative_course_modules__module:first-of-type').classList.add('active')
  document.querySelectorAll('.creative_course_modules__module_header').forEach(function (el) {
    el.addEventListener('click', function (e) {
      e.preventDefault()
      if (el.parentNode.classList.contains('active')) {
        return el.parentNode.classList.remove('active')
      }
      active = document.querySelector('.creative_course_modules__module.active')
      if (active) active.classList.remove('active')
      el.parentNode.classList.add('active')
    })
  })

  var button = block.querySelector('[data-show-more]')
  if (!button) return null
  button.addEventListener('click', function(e) {
    e.preventDefault()
    block.querySelector('.creative_course_modules__modules').classList.add('all')
    button.parentNode.removeChild(button)
  })

}, 'creative_course_modules');

/* File: blocks/creative/business_case_studies/script.js */
flash.ready(function(block) {
  var slider = block.querySelector('.splide')
  if (slider) new Splide(slider, {
      type: 'loop',
      gap: '25px',
      perPage: 2,
      arrows: true,
      arrowPath: 'M0.872849 6.56374L7.15854 0.277917C7.33796 0.098488 7.57711 0 7.8321 0C8.08738 0 8.32638 0.0986295 8.50581 0.277917L9.0765 0.848751C9.25579 1.0279 9.35456 1.26718 9.35456 1.52232C9.35456 1.77731 9.25579 2.02466 9.0765 2.20381L5.40952 5.87886H17.0597C17.585 5.87886 18 6.29007 18 6.81548V7.62249C18 8.1479 17.585 8.60058 17.0597 8.60058H5.36792L9.07636 12.2961C9.25565 12.4756 9.35442 12.7083 9.35442 12.9635C9.35442 13.2183 9.25565 13.4545 9.07636 13.6338L8.50567 14.2028C8.32624 14.3822 8.08724 14.48 7.83196 14.48C7.57697 14.48 7.33782 14.3809 7.15839 14.2015L0.872707 7.91583C0.692854 7.73584 0.593941 7.49556 0.594648 7.24014C0.594082 6.98387 0.692854 6.74346 0.872849 6.56374Z',
      classes: {
          pagination: 'unstyled splide__bullets'
      },
      autoplay: true,
      interval: 7000,
      breakpoints: {
        '940': {
            gap: '20px',
            perPage: 1
        }
    }
  }).mount();
}, 'creative_business_case_studies');

/* File: blocks/creative/brand_enrolment_banner/script.js */
flash.ready(function(block){

	$(document).on('change', 'input.hs-input[type="file"]', function(e) {
		var file = e.target.files[0];
		var name = file.name;
		console.log($(this), $(this).parent().parent(), $(this).parent().parent().find('label'))
		var $p = $(this).parent().parent().find('label p')
		if ($p) $p.remove()
		$(this).parent().parent().find('label').append('<p>' + name +'</p>');
	})

	var logos_number = document.querySelectorAll('.creative_hero_banner__logo_wrapper:nth-child(1) .creative_hero_banner__logo').length;
	var last_position = '';
  var limit = 4
  var duration = flash.compareBreakpoint('medium', '<=') ? 1000 : 800

	if(logos_number > limit) {
		var timestamp = (new Date()).getTime();
		block.setAttribute('data-time', timestamp);
		function changeLogos() {
			if(!document.querySelector('[data-time="' + timestamp + '"]')) {
				return;
			}
			// Getting new position
			var position = null;
			var max = 0;
			do {
				position = getRandomInt(limit) + 1;
				max++;
			} while(last_position === position && max < 50);

			last_position = position;
			
			var new_logo_index = null;
			do {
				new_logo_index = getRandomInt(logos_number);
				max++;
			} while (document.querySelector('[data-time="' + timestamp + '"] .visible[data-index="' + new_logo_index + '"]') && max < 50);

			// Appending the logo
			var new_logo = document.querySelector('[data-time="' + timestamp + '"] .creative_hero_banner__logo_wrapper:nth-child(' + position + ') [data-index="' + new_logo_index + '"]');
			var visible_logo = document.querySelector('[data-time="' + timestamp + '"] .creative_hero_banner__logo_wrapper:nth-child(' + position + ') .visible');
			if(!new_logo || !visible_logo) {
				return;
			}
			new_logo.classList.add('visible');
			visible_logo.classList.add('disappearing');
			if(!enable_slider) {
				return;
			}
			logos_timeout = setTimeout(function(){
				visible_logo.classList.remove('visible');
				visible_logo.classList.remove('disappearing');
				setTimeout(changeLogos, 100);
			}, duration);			
		}
		
		var logos_timeout = setTimeout(changeLogos, 2500);
		var enable_slider = true;

		function getRandomInt(max) {
	  		return Math.floor(Math.random() * Math.floor(max));
		}
	}
}, 'creative_hero_banner');

/* File: assets/js/plugins/follow-height.js */
/**
 * Follow-Height JS 0.1.0
 * ----------------------
 * <AUTHOR> CORBALAN
 * License: MIT
 */

(function(global) {

	// ----------------------------------------------------------------------------- //

	/**
	 * FollowHeight Function
	 * @param object options
	 */
	global.FollowHeight = function(options) {

		// Can be useful guys
		var context = this;

		// Destroyed object ?
		this.destroyed = false;

		// Default options
		this.options = {

			// CSS Selector to use
			selector: '[data-follow-height]',

			// Breakpoint selector for specific elements
			bp_selector: '[data-follow-height-break-on]',

			// Default breakpoint where we wants to reset heights
			break_on: '', // e.g. medium or width (640, 1024, etc.)

			// Default breakpoints, can be changed
			breakpoints: {
				small: 640,
				medium: 1024,
				large: 1230
			}

		};

		// Check if there is custom options
		if(options !== null && typeof options === 'object') {
			this.options = this.mergeOptions(this.options, options);
		}

		// Let's see if our selectors are in the page, if yes update the heights
		var elements = document.querySelector(this.options.selector) !== null;

		window.onload = function () {
			// return (!context.destroyed) ? setTimeout(function(){context.update()},500) : null;
		}

		var resizeTimer = null;
		window.onresize = function(event) {
			clearTimeout(resizeTimer);
			resizeTimer = setTimeout(function() {
				return (!context.destroyed) ? context.update() : null;
			}, 500);
		};

	};

	// ----------------------------------------------------------------------------- //

	// Main Functions
	// -----------------

	/**
	 * Update Function
	 * @param  string selector   
	 * @param  string bp_selector
	 * @return N/A
	 */
	FollowHeight.prototype.update = function(selector, bp_selector) {

		// Context !
		var context = this;

		// Wants to use custom selectors ? Of course you can
		var selector = (typeof selector === 'undefined') ? this.options.selector : selector;
		var bp_selector = (typeof bp_selector === 'undefined') ? this.options.bp_selector : bp_selector;
		var formatted_selector = selector.replace('[', '').replace(']', '');
		var formatted_bp_selector = bp_selector.replace('[', '').replace(']', '');

		// Let's find all the elements and resize them
		var elements = document.querySelectorAll(selector);

		// First, remove the transitions (Fix for safari)
		this.removeTransitions(this.options.selector);

		// Save better heights
		var better_heights = [];

		// Change heights !
		this.forEach(elements, function(i, $target) {

			// Find the better height for this specific attribute id (data-follow-height=myid)
			var id = $target.getAttribute(formatted_selector);
			better_heights[id] = Math.max.apply(null, Array.prototype.map.call(document.querySelectorAll('[' + formatted_selector + '="' + id + '"]'), function(el) {
			    var to_hide = false;
				if(el.offsetParent === null) {
					to_hide = true;
					el.style.display = 'block';
				}
			    var original_style_height = el.style.height;
			    el.style.height = '';
			    var offset_height = el.offsetHeight;
			    el.style.height = original_style_height;
			    if(to_hide) el.style.display = '';
			    return offset_height;
			}));

			if(context.options.break_on != '') {
				if(context.options.break_on % 1 === 0 && window.innerWidth <= context.options.break_on) { // Numeric value
					return $target.style.height = '';
				} else if(context.options.breakpoints[context.options.break_on] && window.innerWidth <= context.options.breakpoints[context.options.break_on]) {
					return $target.style.height = '';
				} else return $target.style.height = better_heights[id] + 'px';
			}
			if($target.hasAttribute(formatted_bp_selector) && ((context.options.breakpoints[$target.getAttribute(formatted_bp_selector)] && window.innerWidth <= context.options.breakpoints[$target.getAttribute(formatted_bp_selector)]) || window.innerWidth <= $target.getAttribute(formatted_bp_selector))) {
				// Meh, it's breakpoint time, let's remove the height
				return $target.style.height = '';
			}
			$target.style.height = better_heights[id] + 'px';

		});

		// Restore Transitions
		this.restoreTransitions(this.options.selector);

	};

	FollowHeight.prototype.destroy = function() {
		var elements = document.querySelectorAll(this.options.selector);
		this.forEach(elements, function(index, $target) {
			return $target.style.height = '';
		});
		return this.destroyed = true;
	};

	// ----------------------------------------------------------------------------- //

	// Helpful Functions
	// -----------------
	
	/**
	 * Merge Options
	 * ref: http://stackoverflow.com/a/171256
	 * @param  object obj1
	 * @param  object obj2
	 * @return object
	 */
	FollowHeight.prototype.mergeOptions = function(primary, secondary){
	    var object = {};
	    for (var attrname in primary) { object[attrname] = primary[attrname]; }
	    for (var attrname in secondary) { object[attrname] = secondary[attrname]; }
	    return object;
	}

	/**
	 * forEach Function
	 * @param  array    array   
	 * @param  function callback
	 * @param  object   scope   
	 * @return N/A
	 */
	FollowHeight.prototype.forEach = function (array, callback, scope) {
		for (var i = 0; i < array.length; i++) {
			callback.call(scope, i, array[i]);
		}
	};

	/**
	 * Remove Transitions from Elements
	 * @param  string selector CSS/JS Selector
	 * @return N/A
	 */
	FollowHeight.prototype.removeTransitions = function(selector) {
		var elements = document.querySelectorAll(selector);
		this.forEach(elements, function(index, $target) {
			$target.style.webkitTransition = 'none';
			$target.style.mozTransition = 'none';
			$target.style.msTransition = 'none';
			$target.style.oTransition = 'none';
			$target.style.transition = 'none';
		});
	};

	/**
	 * Restore Transitions for Elements
	 * @param  string selector CSS/JS Selector
	 * @return N/A
	 */
	FollowHeight.prototype.restoreTransitions = function(selector) {
		var elements = document.querySelectorAll(selector);
		this.forEach(elements, function(index, $target) {
			$target.style.webkitTransition = '';
			$target.style.mozTransition = '';
			$target.style.msTransition = '';
			$target.style.oTransition = '';
			$target.style.transition = '';
		});
	};

	// ----------------------------------------------------------------------------- //
	
	global.followHeightInstance = new FollowHeight();

})(window);

/* File: assets/js/plugins/splide.min.js */
/*!
 * Splide.js
 * Version  : 2.4.20
 * License  : MIT
 * Copyright: 2020 Naotoshi Fujita
 */!function(){"use strict";var t={d:function(n,e){for(var i in e)t.o(e,i)&&!t.o(n,i)&&Object.defineProperty(n,i,{enumerable:!0,get:e[i]})},o:function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},r:function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},n={};t.r(n),t.d(n,{CREATED:function(){return R},DESTROYED:function(){return X},IDLE:function(){return F},MOUNTED:function(){return B},MOVING:function(){return G}});function e(){return(e=Object.assign||function(t){for(var n=1;n<arguments.length;n++){var e=arguments[n];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])}return t}).apply(this,arguments)}var i=Object.keys;function o(t,n){i(t).some((function(e,i){return n(t[e],e,i)}))}function r(t){return i(t).map((function(n){return t[n]}))}function s(t){return"object"==typeof t}function a(t,n){var i=e({},t);return o(n,(function(t,n){s(t)?(s(i[n])||(i[n]={}),i[n]=a(i[n],t)):i[n]=t})),i}function u(t){return Array.isArray(t)?t:[t]}function c(t,n,e){return Math.min(Math.max(t,n>e?e:n),n>e?n:e)}function d(t,n){var e=0;return t.replace(/%s/g,(function(){return u(n)[e++]}))}function f(t){var n=typeof t;return"number"===n&&t>0?parseFloat(t)+"px":"string"===n?t:""}function l(t){return t<10?"0"+t:t}function h(t,n){if("string"==typeof n){var e=m("div",{});E(e,{position:"absolute",width:n}),w(t,e),n=e.clientWidth,b(e)}return+n||0}function p(t,n){return t?t.querySelector(n.split(" ")[0]):null}function g(t,n){return v(t,n)[0]}function v(t,n){return t?r(t.children).filter((function(t){return P(t,n.split(" ")[0])||t.tagName===n})):[]}function m(t,n){var e=document.createElement(t);return o(n,(function(t,n){return C(e,n,t)})),e}function y(t){var n=m("div",{});return n.innerHTML=t,n.firstChild}function b(t){u(t).forEach((function(t){if(t){var n=t.parentElement;n&&n.removeChild(t)}}))}function w(t,n){t&&t.appendChild(n)}function x(t,n){if(t&&n){var e=n.parentElement;e&&e.insertBefore(t,n)}}function E(t,n){t&&o(n,(function(n,e){null!==n&&(t.style[e]=n)}))}function _(t,n,e){t&&u(n).forEach((function(n){n&&t.classList[e?"remove":"add"](n)}))}function k(t,n){_(t,n,!1)}function S(t,n){_(t,n,!0)}function P(t,n){return!!t&&t.classList.contains(n)}function C(t,n,e){t&&t.setAttribute(n,e)}function z(t,n){return t?t.getAttribute(n):""}function I(t,n){u(n).forEach((function(n){u(t).forEach((function(t){return t&&t.removeAttribute(n)}))}))}function M(t){return t.getBoundingClientRect()}var T="slide",A="loop",O="fade",L=function(t,n){var e,i;return{mount:function(){e=n.Elements.list,t.on("transitionend",(function(t){t.target===e&&i&&i()}),e)},start:function(o,r,s,a,u){var c=t.options,d=n.Controller.edgeIndex,f=c.speed;i=u,t.is(T)&&(0===s&&r>=d||s>=d&&0===r)&&(f=c.rewindSpeed||f),E(e,{transition:"transform "+f+"ms "+c.easing,transform:"translate("+a.x+"px,"+a.y+"px)"})}}},W=function(t,n){function e(e){var i=t.options;E(n.Elements.slides[e],{transition:"opacity "+i.speed+"ms "+i.easing})}return{mount:function(){e(t.index)},start:function(t,i,o,r,s){var a=n.Elements.track;E(a,{height:f(a.clientHeight)}),e(i),setTimeout((function(){s(),E(a,{height:""})}))}}};function H(t){console.error("[SPLIDE] "+t)}function j(t,n){if(!t)throw new Error(n)}var q="splide",D={active:"is-active",visible:"is-visible",loading:"is-loading"},N={type:"slide",rewind:!1,speed:400,rewindSpeed:0,waitForTransition:!0,width:0,height:0,fixedWidth:0,fixedHeight:0,heightRatio:0,autoWidth:!1,autoHeight:!1,perPage:1,perMove:0,clones:0,start:0,focus:!1,gap:0,padding:0,arrows:!0,arrowPath:"",pagination:!0,autoplay:!1,interval:5e3,pauseOnHover:!0,pauseOnFocus:!0,resetProgress:!0,lazyLoad:!1,preloadPages:1,easing:"cubic-bezier(.42,.65,.27,.99)",keyboard:"global",drag:!0,dragAngleThreshold:30,swipeDistanceThreshold:150,flickVelocityThreshold:.6,flickPower:600,flickMaxPages:1,direction:"ltr",cover:!1,accessibility:!0,slideFocus:!0,isNavigation:!1,trimSpace:!0,updateOnMove:!1,throttle:100,destroy:!1,breakpoints:!1,classes:{root:q,slider:q+"__slider",track:q+"__track",list:q+"__list",slide:q+"__slide",container:q+"__slide__container",arrows:q+"__arrows",arrow:q+"__arrow",prev:q+"__arrow--prev",next:q+"__arrow--next",pagination:q+"__pagination",page:q+"__pagination__page",clone:q+"__slide--clone",progress:q+"__progress",bar:q+"__progress__bar",autoplay:q+"__autoplay",play:q+"__play",pause:q+"__pause",spinner:q+"__spinner",sr:q+"__sr"},i18n:{prev:"Previous slide",next:"Next slide",first:"Go to first slide",last:"Go to last slide",slideX:"Go to slide %s",pageX:"Go to page %s",play:"Start autoplay",pause:"Pause autoplay"}},R=1,B=2,F=3,G=4,X=5;function V(t,n){for(var e=0;e<n.length;e++){var i=n[e];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var U=function(){function t(t,e,i){var o;void 0===e&&(e={}),void 0===i&&(i={}),this.root=t instanceof Element?t:document.querySelector(t),j(this.root,"An invalid element/selector was given."),this.Components=null,this.Event=function(){var t=[];function n(t){t.elm&&t.elm.removeEventListener(t.event,t.handler,t.options)}return{on:function(n,e,i,o){void 0===i&&(i=null),void 0===o&&(o={}),n.split(" ").forEach((function(n){i&&i.addEventListener(n,e,o),t.push({event:n,handler:e,elm:i,options:o})}))},off:function(e,i){void 0===i&&(i=null),e.split(" ").forEach((function(e){t=t.filter((function(t){return!t||t.event!==e||t.elm!==i||(n(t),!1)}))}))},emit:function(n){for(var e=arguments.length,i=new Array(e>1?e-1:0),o=1;o<e;o++)i[o-1]=arguments[o];t.forEach((function(t){t.elm||t.event.split(".")[0]!==n||t.handler.apply(t,i)}))},destroy:function(){t.forEach(n),t=[]}}}(),this.State=(o=R,{set:function(t){o=t},is:function(t){return t===o}}),this.STATES=n,this._o=a(N,e),this._i=0,this._c=i,this._e={},this._t=null}var e,i,s,u=t.prototype;return u.mount=function(t,n){var e=this;void 0===t&&(t=this._e),void 0===n&&(n=this._t),this.State.set(R),this._e=t,this._t=n,this.Components=function(t,n,e){var i={};return o(n,(function(n,e){i[e]=n(t,i,e.toLowerCase())})),e||(e=t.is(O)?W:L),i.Transition=e(t,i),i}(this,a(this._c,t),n);try{o(this.Components,(function(t,n){var i=t.required;void 0===i||i?t.mount&&t.mount():delete e.Components[n]}))}catch(t){return void H(t.message)}var i=this.State;return i.set(B),o(this.Components,(function(t){t.mounted&&t.mounted()})),this.emit("mounted"),i.set(F),this.emit("ready"),E(this.root,{visibility:"visible"}),this.on("move drag",(function(){return i.set(G)})).on("moved dragged",(function(){return i.set(F)})),this},u.sync=function(t){return this.sibling=t,this},u.on=function(t,n,e,i){return void 0===e&&(e=null),void 0===i&&(i={}),this.Event.on(t,n,e,i),this},u.off=function(t,n){return void 0===n&&(n=null),this.Event.off(t,n),this},u.emit=function(t){for(var n,e=arguments.length,i=new Array(e>1?e-1:0),o=1;o<e;o++)i[o-1]=arguments[o];return(n=this.Event).emit.apply(n,[t].concat(i)),this},u.go=function(t,n){return void 0===n&&(n=this.options.waitForTransition),(this.State.is(F)||this.State.is(G)&&!n)&&this.Components.Controller.go(t,!1),this},u.is=function(t){return t===this._o.type},u.add=function(t,n){return void 0===n&&(n=-1),this.Components.Elements.add(t,n,this.refresh.bind(this)),this},u.remove=function(t){return this.Components.Elements.remove(t),this.refresh(),this},u.refresh=function(){return this.emit("refresh:before").emit("refresh").emit("resize"),this},u.destroy=function(t){var n=this;if(void 0===t&&(t=!0),!this.State.is(R))return r(this.Components).reverse().forEach((function(n){n.destroy&&n.destroy(t)})),this.emit("destroy",t),this.Event.destroy(),this.State.set(X),this;this.on("ready",(function(){return n.destroy(t)}))},e=t,(i=[{key:"index",get:function(){return this._i},set:function(t){this._i=parseInt(t)}},{key:"length",get:function(){return this.Components.Elements.length}},{key:"options",get:function(){return this._o},set:function(t){var n=this.State.is(R);n||this.emit("update"),this._o=a(this._o,t),n||this.emit("updated",this._o)}},{key:"classes",get:function(){return this._o.classes}},{key:"i18n",get:function(){return this._o.i18n}}])&&V(e.prototype,i),s&&V(e,s),t}(),Y=function(t){var n=z(t.root,"data-splide");if(n)try{t.options=JSON.parse(n)}catch(t){H(t.message)}return{mount:function(){t.State.is(R)&&(t.index=t.options.start)}}},J="rtl",K="ttb",Q="update.slide",Z=function(t,n){var e=t.root,i=t.classes,s=[];if(!e.id){window.splide=window.splide||{};var a=window.splide.uid||0;window.splide.uid=++a,e.id="splide"+l(a)}var u={mount:function(){var n=this;this.init(),t.on("refresh",(function(){n.destroy(),n.init()})).on("updated",(function(){S(e,c()),k(e,c())}))},destroy:function(){s.forEach((function(t){t.destroy()})),s=[],S(e,c())},init:function(){var t=this;!function(){u.slider=g(e,i.slider),u.track=p(e,"."+i.track),u.list=g(u.track,i.list),j(u.track&&u.list,"Track or list was not found."),u.slides=v(u.list,i.slide);var t=d(i.arrows);u.arrows={prev:p(t,"."+i.prev),next:p(t,"."+i.next)};var n=d(i.autoplay);u.bar=p(d(i.progress),"."+i.bar),u.play=p(n,"."+i.play),u.pause=p(n,"."+i.pause),u.track.id=u.track.id||e.id+"-track",u.list.id=u.list.id||e.id+"-list"}(),k(e,c()),this.slides.forEach((function(n,e){t.register(n,e,-1)}))},register:function(n,e,i){var o=function(t,n,e,i){var o=t.options.updateOnMove,s="ready.slide updated.slide resized.slide moved.slide"+(o?" move.slide":""),a={slide:i,index:n,realIndex:e,container:g(i,t.classes.container),isClone:e>-1,mount:function(){var r=this;this.isClone||(i.id=t.root.id+"-slide"+l(n+1)),t.on(s,(function(){return r.update()})).on(Q,c).on("click",(function(){return t.emit("click",r)}),i),o&&t.on("move.slide",(function(t){t===e&&u(!0,!1)})),E(i,{display:""}),this.styles=z(i,"style")||""},destroy:function(){t.off(s).off(Q).off("click",i),S(i,r(D)),c(),I(this.container,"style")},update:function(){u(this.isActive(),!1),u(this.isVisible(),!0)},isActive:function(){return t.index===n},isVisible:function(){var n=this.isActive();if(t.is(O)||n)return n;var e=Math.ceil,o=M(t.Components.Elements.track),r=M(i);return t.options.direction===K?o.top<=r.top&&r.bottom<=e(o.bottom):o.left<=r.left&&r.right<=e(o.right)},isWithin:function(e,i){var o=Math.abs(e-n);return t.is(T)||this.isClone||(o=Math.min(o,t.length-o)),o<i}};function u(n,e){var o=e?"visible":"active",r=D[o];n?(k(i,r),t.emit(""+o,a)):P(i,r)&&(S(i,r),t.emit(e?"hidden":"inactive",a))}function c(){C(i,"style",a.styles)}return a}(t,e,i,n);o.mount(),s.push(o)},getSlide:function(t){return s.filter((function(n){return n.index===t}))[0]},getSlides:function(t){return t?s:s.filter((function(t){return!t.isClone}))},getSlidesByPage:function(e){var i=n.Controller.toIndex(e),o=t.options,r=!1!==o.focus?1:o.perPage;return s.filter((function(t){var n=t.index;return i<=n&&n<i+r}))},add:function(t,n,e){if("string"==typeof t&&(t=y(t)),t instanceof Element){var i=this.slides[n];E(t,{display:"none"}),i?(x(t,i),this.slides.splice(n,0,t)):(w(this.list,t),this.slides.push(t)),function(t,n){var e=t.querySelectorAll("img"),i=e.length;if(i){var r=0;o(e,(function(t){t.onload=t.onerror=function(){++r===i&&n()}}))}else n()}(t,(function(){e&&e(t)}))}},remove:function(t){b(this.slides.splice(t,1)[0])},each:function(t){s.forEach(t)},get length(){return this.slides.length},get total(){return s.length}};function c(){var n=i.root,e=t.options;return[n+"--"+e.type,n+"--"+e.direction,e.drag?n+"--draggable":"",e.isNavigation?n+"--nav":"",D.active]}function d(t){return g(e,t)||g(u.slider,t)}return u},$=Math.floor,tt=function(t,n){var e,i,o={mount:function(){e=t.options,i=t.is(A),t.on("move",(function(n){t.index=n})).on("updated refresh",(function(n){e=n||e,t.index=c(t.index,0,o.edgeIndex)}))},go:function(t,e){var i=this.trim(this.parse(t));n.Track.go(i,this.rewind(i),e)},parse:function(n){var i=t.index,r=String(n).match(/([+\-<>]+)(\d+)?/),s=r?r[1]:"",a=r?parseInt(r[2]):0;switch(s){case"+":i+=a||1;break;case"-":i-=a||1;break;case">":case"<":i=function(t,n,i){if(t>-1)return o.toIndex(t);var r=e.perMove,s=i?-1:1;if(r)return n+r*s;return o.toIndex(o.toPage(n)+s)}(a,i,"<"===s);break;default:i=parseInt(n)}return i},toIndex:function(n){if(r())return n;var i=t.length,o=e.perPage,s=n*o;return i-o<=(s-=(this.pageLength*o-i)*$(s/i))&&s<i&&(s=i-o),s},toPage:function(n){if(r())return n;var i=t.length,o=e.perPage;return $(i-o<=n&&n<i?(i-1)/o:n/o)},trim:function(t){return i||(t=e.rewind?this.rewind(t):c(t,0,this.edgeIndex)),t},rewind:function(t){var n=this.edgeIndex;if(i){for(;t>n;)t-=n+1;for(;t<0;)t+=n+1}else t>n?t=0:t<0&&(t=n);return t},isRtl:function(){return e.direction===J},get pageLength(){var n=t.length;return r()?n:Math.ceil(n/e.perPage)},get edgeIndex(){var n=t.length;return n?r()||e.isNavigation||i?n-1:n-e.perPage:0},get prevIndex(){var n=t.index-1;return(i||e.rewind)&&(n=this.rewind(n)),n>-1?n:-1},get nextIndex(){var n=t.index+1;return(i||e.rewind)&&(n=this.rewind(n)),t.index<n&&n<=this.edgeIndex||0===n?n:-1}};function r(){return!1!==e.focus}return o},nt=Math.abs,et=function(t,n){var e,i,o,r=t.options.direction===K,s=t.is(O),a=t.options.direction===J,u=!1,d=a?1:-1,f={sign:d,mount:function(){i=n.Elements,e=n.Layout,o=i.list},mounted:function(){var n=this;s||(this.jump(0),t.on("mounted resize updated",(function(){n.jump(t.index)})))},go:function(e,i,o){var r=h(e),a=t.index;t.State.is(G)&&u||(u=e!==i,o||t.emit("move",i,a,e),Math.abs(r-this.position)>=1||s?n.Transition.start(e,i,a,this.toCoord(r),(function(){l(e,i,a,o)})):e!==a&&"move"===t.options.trimSpace?n.Controller.go(e+e-a,o):l(e,i,a,o))},jump:function(t){this.translate(h(t))},translate:function(t){E(o,{transform:"translate"+(r?"Y":"X")+"("+t+"px)"})},cancel:function(){t.is(A)?this.shift():this.translate(this.position),E(o,{transition:""})},shift:function(){var n=nt(this.position),e=nt(this.toPosition(0)),i=nt(this.toPosition(t.length)),o=i-e;n<e?n+=o:n>i&&(n-=o),this.translate(d*n)},trim:function(n){return!t.options.trimSpace||t.is(A)?n:c(n,d*(e.totalSize()-e.size-e.gap),0)},toIndex:function(t){var n=this,e=0,o=1/0;return i.getSlides(!0).forEach((function(i){var r=i.index,s=nt(n.toPosition(r)-t);s<o&&(o=s,e=r)})),e},toCoord:function(t){return{x:r?0:t,y:r?t:0}},toPosition:function(t){var n=e.totalSize(t)-e.slideSize(t)-e.gap;return d*(n+this.offset(t))},offset:function(n){var i=t.options.focus,o=e.slideSize(n);return"center"===i?-(e.size-o)/2:-(parseInt(i)||0)*(o+e.gap)},get position(){var t=r?"top":a?"right":"left";return M(o)[t]-(M(i.track)[t]-e.padding[t]*d)}};function l(n,e,i,r){E(o,{transition:""}),u=!1,s||f.jump(e),r||t.emit("moved",e,i,n)}function h(t){return f.trim(f.toPosition(t))}return f},it=function(t,n){var e=[],i=0,o=n.Elements,r={mount:function(){var n=this;t.is(A)&&(s(),t.on("refresh:before",(function(){n.destroy()})).on("refresh",s).on("resize",(function(){i!==a()&&(n.destroy(),t.refresh())})))},destroy:function(){b(e),e=[]},get clones(){return e},get length(){return e.length}};function s(){r.destroy(),function(t){var n=o.length,i=o.register;if(n){for(var r=o.slides;r.length<t;)r=r.concat(r);r.slice(0,t).forEach((function(t,r){var s=u(t);w(o.list,s),e.push(s),i(s,r+n,r%n)})),r.slice(-t).forEach((function(o,s){var a=u(o);x(a,r[0]),e.push(a),i(a,s-t,(n+s-t%n)%n)}))}}(i=a())}function a(){var n=t.options;if(n.clones)return n.clones;var e=n.autoWidth||n.autoHeight?o.length:n.perPage,i=n.direction===K?"Height":"Width",r=h(t.root,n["fixed"+i]);return r&&(e=Math.ceil(o.track["client"+i]/r)),e*(n.drag?n.flickMaxPages+1:1)}function u(n){var e=n.cloneNode(!0);return k(e,t.classes.clone),I(e,"id"),e}return r};function ot(t,n){var e;return function(){e||(e=setTimeout((function(){t(),e=null}),n))}}var rt=function(t,n){var e,o,r=n.Elements,s=t.options.direction===K,a=(e={mount:function(){t.on("resize load",ot((function(){t.emit("resize")}),t.options.throttle),window).on("resize",c).on("updated refresh",u),u(),this.totalSize=s?this.totalHeight:this.totalWidth,this.slideSize=s?this.slideHeight:this.slideWidth},destroy:function(){I([r.list,r.track],"style")},get size(){return s?this.height:this.width}},o=s?function(t,n){var e,i,o=n.Elements,r=t.root;return{margin:"marginBottom",init:function(){this.resize()},resize:function(){i=t.options,e=o.track,this.gap=h(r,i.gap);var n=i.padding,s=h(r,n.top||n),a=h(r,n.bottom||n);this.padding={top:s,bottom:a},E(e,{paddingTop:f(s),paddingBottom:f(a)})},totalHeight:function(n){void 0===n&&(n=t.length-1);var e=o.getSlide(n);return e?M(e.slide).bottom-M(o.list).top+this.gap:0},slideWidth:function(){return h(r,i.fixedWidth||this.width)},slideHeight:function(t){if(i.autoHeight){var n=o.getSlide(t);return n?n.slide.offsetHeight:0}var e=i.fixedHeight||(this.height+this.gap)/i.perPage-this.gap;return h(r,e)},get width(){return e.clientWidth},get height(){var t=i.height||this.width*i.heightRatio;return j(t,'"height" or "heightRatio" is missing.'),h(r,t)-this.padding.top-this.padding.bottom}}}(t,n):function(t,n){var e,i=n.Elements,o=t.root,r=t.options;return{margin:"margin"+(r.direction===J?"Left":"Right"),height:0,init:function(){this.resize()},resize:function(){r=t.options,e=i.track,this.gap=h(o,r.gap);var n=r.padding,s=h(o,n.left||n),a=h(o,n.right||n);this.padding={left:s,right:a},E(e,{paddingLeft:f(s),paddingRight:f(a)})},totalWidth:function(n){void 0===n&&(n=t.length-1);var e=i.getSlide(n),o=0;if(e){var s=M(e.slide),a=M(i.list);o=r.direction===J?a.right-s.left:s.right-a.left,o+=this.gap}return o},slideWidth:function(t){if(r.autoWidth){var n=i.getSlide(t);return n?n.slide.offsetWidth:0}var e=r.fixedWidth||(this.width+this.gap)/r.perPage-this.gap;return h(o,e)},slideHeight:function(){var t=r.height||r.fixedHeight||this.width*r.heightRatio;return h(o,t)},get width(){return e.clientWidth-this.padding.left-this.padding.right}}}(t,n),i(o).forEach((function(t){e[t]||Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))})),e);function u(){a.init(),E(t.root,{maxWidth:f(t.options.width)}),r.each((function(t){t.slide.style[a.margin]=f(a.gap)})),c()}function c(){var n=t.options;a.resize(),E(r.track,{height:f(a.height)});var e=n.autoHeight?null:f(a.slideHeight());r.each((function(t){E(t.container,{height:e}),E(t.slide,{width:n.autoWidth?null:f(a.slideWidth(t.index)),height:t.container?null:e})})),t.emit("resized")}return a},st=Math.abs,at=function(t,n){var e,i,r,s,a=n.Track,u=n.Controller,d=t.options.direction===K,f=d?"y":"x",l={disabled:!1,mount:function(){var e=this,i=n.Elements,r=i.track;t.on("touchstart mousedown",h,r).on("touchmove mousemove",g,r,{passive:!1}).on("touchend touchcancel mouseleave mouseup dragend",v,r).on("mounted refresh",(function(){o(i.list.querySelectorAll("img, a"),(function(n){t.off("dragstart",n).on("dragstart",(function(t){t.preventDefault()}),n,{passive:!1})}))})).on("mounted updated",(function(){e.disabled=!t.options.drag}))}};function h(t){l.disabled||s||p(t)}function p(t){e=a.toCoord(a.position),i=m(t,{}),r=i}function g(n){if(i)if(r=m(n,i),s){if(n.cancelable&&n.preventDefault(),!t.is(O)){var o=e[f]+r.offset[f];a.translate(function(n){if(t.is(T)){var e=a.sign,i=e*a.trim(a.toPosition(0)),o=e*a.trim(a.toPosition(u.edgeIndex));(n*=e)<i?n=i-7*Math.log(i-n):n>o&&(n=o+7*Math.log(n-o)),n*=e}return n}(o))}}else(function(n){var e=n.offset;if(t.State.is(G)&&t.options.waitForTransition)return!1;var i=180*Math.atan(st(e.y)/st(e.x))/Math.PI;d&&(i=90-i);return i<t.options.dragAngleThreshold})(r)&&(t.emit("drag",i),s=!0,a.cancel(),p(n))}function v(){i=null,s&&(t.emit("dragged",r),function(e){var i=e.velocity[f],o=st(i);if(o>0){var r=t.options,s=t.index,d=i<0?-1:1,l=s;if(!t.is(O)){var h=a.position;o>r.flickVelocityThreshold&&st(e.offset[f])<r.swipeDistanceThreshold&&(h+=d*Math.min(o*r.flickPower,n.Layout.size*(r.flickMaxPages||1))),l=a.toIndex(h)}l===s&&o>.1&&(l=s+d*a.sign),t.is(T)&&(l=c(l,0,u.edgeIndex)),u.go(l,r.isNavigation)}}(r),s=!1)}function m(t,n){var e=t.timeStamp,i=t.touches,o=i?i[0]:t,r=o.clientX,s=o.clientY,a=n.to||{},u=a.x,c=void 0===u?r:u,d=a.y,f={x:r-c,y:s-(void 0===d?s:d)},l=e-(n.time||0);return{to:{x:r,y:s},offset:f,time:e,velocity:{x:f.x/l,y:f.y/l}}}return l},ut=function(t,n){var e=!1;function i(t){e&&(t.preventDefault(),t.stopPropagation(),t.stopImmediatePropagation())}return{required:t.options.drag,mount:function(){t.on("click",i,n.Elements.track,{capture:!0}).on("drag",(function(){e=!0})).on("dragged",(function(){setTimeout((function(){e=!1}))}))}}},ct=1,dt=2,ft=3,lt=function(t,n,e){var i,o,r,s=t.classes,a=t.root,u=n.Elements;function c(){var r=n.Controller,s=r.prevIndex,a=r.nextIndex,u=t.length>t.options.perPage||t.is(A);i.disabled=s<0||!u,o.disabled=a<0||!u,t.emit(e+":updated",i,o,s,a)}function d(n){return y('<button class="'+s.arrow+" "+(n?s.prev:s.next)+'" type="button"><svg xmlns="http://www.w3.org/2000/svg"\tviewBox="0 0 40 40"\twidth="40"\theight="40"><path d="'+(t.options.arrowPath||"m15.5 0.932-4.3 4.38 14.5 14.6-14.5 14.5 4.3 4.4 14.6-14.6 4.4-4.3-4.4-4.4-14.6-14.6z")+'" />')}return{required:t.options.arrows,mount:function(){i=u.arrows.prev,o=u.arrows.next,i&&o||!t.options.arrows||(i=d(!0),o=d(!1),r=!0,function(){var n=m("div",{class:s.arrows});w(n,i),w(n,o);var e=u.slider,r="slider"===t.options.arrows&&e?e:a;x(n,r.firstElementChild)}()),i&&o&&t.on("click",(function(){t.go("<")}),i).on("click",(function(){t.go(">")}),o).on("mounted move updated refresh",c),this.arrows={prev:i,next:o}},mounted:function(){t.emit(e+":mounted",i,o)},destroy:function(){I([i,o],"disabled"),r&&b(i.parentElement)}}},ht="move.page",pt="updated.page refresh.page",gt=function(t,n,e){var i={},o=n.Elements,r={mount:function(){var n=t.options.pagination;if(n){i=function(){var n=t.options,e=t.classes,i=m("ul",{class:e.pagination}),r=o.getSlides(!1).filter((function(t){return!1!==n.focus||t.index%n.perPage==0})).map((function(n,r){var s=m("li",{}),a=m("button",{class:e.page,type:"button"});return w(s,a),w(i,s),t.on("click",(function(){t.go(">"+r)}),a),{li:s,button:a,page:r,Slides:o.getSlidesByPage(r)}}));return{list:i,items:r}}();var e=o.slider;w("slider"===n&&e?e:t.root,i.list),t.on(ht,s)}t.off(pt).on(pt,(function(){r.destroy(),t.options.pagination&&(r.mount(),r.mounted())}))},mounted:function(){if(t.options.pagination){var n=t.index;t.emit(e+":mounted",i,this.getItem(n)),s(n,-1)}},destroy:function(){b(i.list),i.items&&i.items.forEach((function(n){t.off("click",n.button)})),t.off(ht),i={}},getItem:function(t){return i.items[n.Controller.toPage(t)]},get data(){return i}};function s(n,o){var s=r.getItem(o),a=r.getItem(n),u=D.active;s&&S(s.button,u),a&&k(a.button,u),t.emit(e+":updated",i,s,a)}return r},vt="data-splide-lazy",mt="data-splide-lazy-srcset",yt="aria-current",bt="aria-controls",wt="aria-label",xt="aria-hidden",Et="tabindex",_t={ltr:{ArrowLeft:"<",ArrowRight:">",Left:"<",Right:">"},rtl:{ArrowLeft:">",ArrowRight:"<",Left:">",Right:"<"},ttb:{ArrowUp:"<",ArrowDown:">",Up:"<",Down:">"}},kt=function(t,n){var e=t.i18n,i=n.Elements,o=[xt,Et,bt,wt,yt,"role"];function r(n,e){C(n,xt,!e),t.options.slideFocus&&C(n,Et,e?0:-1)}function s(t,n){var e=i.track.id;C(t,bt,e),C(n,bt,e)}function a(n,i,o,r){var s=t.index,a=o>-1&&s<o?e.last:e.prev,u=r>-1&&s>r?e.first:e.next;C(n,wt,a),C(i,wt,u)}function u(n,i){i&&C(i.button,yt,!0),n.items.forEach((function(n){var i=t.options,o=d(!1===i.focus&&i.perPage>1?e.pageX:e.slideX,n.page+1),r=n.button,s=n.Slides.map((function(t){return t.slide.id}));C(r,bt,s.join(" ")),C(r,wt,o)}))}function c(t,n,e){n&&I(n.button,yt),e&&C(e.button,yt,!0)}function f(t){i.each((function(n){var i=n.slide,o=n.realIndex;h(i)||C(i,"role","button");var r=o>-1?o:n.index,s=d(e.slideX,r+1),a=t.Components.Elements.getSlide(r);C(i,wt,s),a&&C(i,bt,a.slide.id)}))}function l(t,n){var e=t.slide;n?C(e,yt,!0):I(e,yt)}function h(t){return"BUTTON"===t.tagName}return{required:t.options.accessibility,mount:function(){t.on("visible",(function(t){r(t.slide,!0)})).on("hidden",(function(t){r(t.slide,!1)})).on("arrows:mounted",s).on("arrows:updated",a).on("pagination:mounted",u).on("pagination:updated",c).on("refresh",(function(){I(n.Clones.clones,o)})),t.options.isNavigation&&t.on("navigation:mounted navigation:updated",f).on("active",(function(t){l(t,!0)})).on("inactive",(function(t){l(t,!1)})),["play","pause"].forEach((function(t){var n=i[t];n&&(h(n)||C(n,"role","button"),C(n,bt,i.track.id),C(n,wt,e[t]))}))},destroy:function(){var t=n.Arrows,e=t?t.arrows:{};I(i.slides.concat([e.prev,e.next,i.play,i.pause]),o)}}},St="move.sync",Pt="mouseup touchend",Ct=[" ","Enter","Spacebar"],zt={Options:Y,Breakpoints:function(t){var n,e,i=t.options.breakpoints,o=ot(s,50),r=[];function s(){var o,s=(o=r.filter((function(t){return t.mql.matches}))[0])?o.point:-1;if(s!==e){e=s;var a=t.State,u=i[s]||n,c=u.destroy;c?(t.options=n,t.destroy("completely"===c)):(a.is(X)&&t.mount(),t.options=u)}}return{required:i&&matchMedia,mount:function(){r=Object.keys(i).sort((function(t,n){return+t-+n})).map((function(t){return{point:t,mql:matchMedia("(max-width:"+t+"px)")}})),this.destroy(!0),addEventListener("resize",o),n=t.options,s()},destroy:function(t){t&&removeEventListener("resize",o)}}},Controller:tt,Elements:Z,Track:et,Clones:it,Layout:rt,Drag:at,Click:ut,Autoplay:function(t,n,e){var i,o=[],r=n.Elements,s={required:t.options.autoplay,mount:function(){var n=t.options;r.slides.length>n.perPage&&(i=function(t,n,e){var i,o,r,s=window.requestAnimationFrame,a=!0,u=function u(c){a||(i||(i=c,r&&r<1&&(i-=r*n)),r=(o=c-i)/n,o>=n&&(i=0,r=1,t()),e&&e(r),s(u))};return{pause:function(){a=!0,i=0},play:function(t){i=0,t&&(r=0),a&&(a=!1,s(u))}}}((function(){t.go(">")}),n.interval,(function(n){t.emit(e+":playing",n),r.bar&&E(r.bar,{width:100*n+"%"})})),function(){var n=t.options,e=t.sibling,i=[t.root,e?e.root:null];n.pauseOnHover&&(a(i,"mouseleave",ct,!0),a(i,"mouseenter",ct,!1));n.pauseOnFocus&&(a(i,"focusout",dt,!0),a(i,"focusin",dt,!1));r.play&&t.on("click",(function(){s.play(dt),s.play(ft)}),r.play);r.pause&&a([r.pause],"click",ft,!1);t.on("move refresh",(function(){s.play()})).on("destroy",(function(){s.pause()}))}(),this.play())},play:function(n){void 0===n&&(n=0),(o=o.filter((function(t){return t!==n}))).length||(t.emit(e+":play"),i.play(t.options.resetProgress))},pause:function(n){void 0===n&&(n=0),i.pause(),-1===o.indexOf(n)&&o.push(n),1===o.length&&t.emit(e+":pause")}};function a(n,e,i,o){n.forEach((function(n){t.on(e,(function(){s[o?"play":"pause"](i)}),n)}))}return s},Cover:function(t,n){function e(t){n.Elements.each((function(n){var e=g(n.slide,"IMG")||g(n.container,"IMG");e&&e.src&&i(e,t)}))}function i(t,n){E(t.parentElement,{background:n?"":'center/cover no-repeat url("'+t.src+'")'}),E(t,{display:n?"":"none"})}return{required:t.options.cover,mount:function(){t.on("lazyload:loaded",(function(t){i(t,!1)})),t.on("mounted updated refresh",(function(){return e(!1)}))},destroy:function(){e(!0)}}},Arrows:lt,Pagination:gt,LazyLoad:function(t,n,e){var i,r,s=t.options,a="sequential"===s.lazyLoad;function u(){r=[],i=0}function c(n){n=isNaN(n)?t.index:n,(r=r.filter((function(t){return!t.Slide.isWithin(n,s.perPage*(s.preloadPages+1))||(d(t.img,t.Slide),!1)})))[0]||t.off("moved."+e)}function d(n,e){k(e.slide,D.loading);var i=m("span",{class:t.classes.spinner});w(n.parentElement,i),n.onload=function(){l(n,i,e,!1)},n.onerror=function(){l(n,i,e,!0)},C(n,"srcset",z(n,mt)||""),C(n,"src",z(n,vt)||"")}function f(){if(i<r.length){var t=r[i];d(t.img,t.Slide)}i++}function l(n,i,o,r){S(o.slide,D.loading),r||(b(i),E(n,{display:""}),t.emit(e+":loaded",n).emit("resize")),a&&f()}return{required:s.lazyLoad,mount:function(){t.on("mounted refresh",(function(){u(),n.Elements.each((function(t){o(t.slide.querySelectorAll("[data-splide-lazy], ["+mt+"]"),(function(n){n.src||n.srcset||(r.push({img:n,Slide:t}),E(n,{display:"none"}))}))})),a&&f()})),a||t.on("mounted refresh moved."+e,c)},destroy:u}},Keyboard:function(t){var n;return{mount:function(){t.on("mounted updated",(function(){var e=t.options,i=t.root,o=_t[e.direction],r=e.keyboard;n&&(t.off("keydown",n),I(i,Et)),r&&("focused"===r?(n=i,C(i,Et,0)):n=document,t.on("keydown",(function(n){o[n.key]&&t.go(o[n.key])}),n))}))}}},Sync:function(t){var n=t.sibling,e=n&&n.options.isNavigation;function i(){t.on(St,(function(t,e,i){n.off(St).go(n.is(A)?i:t,!1),o()}))}function o(){n.on(St,(function(n,e,o){t.off(St).go(t.is(A)?o:n,!1),i()}))}function r(){n.Components.Elements.each((function(n){var e=n.slide,i=n.index;t.off(Pt,e).on(Pt,(function(t){t.button&&0!==t.button||s(i)}),e),t.off("keyup",e).on("keyup",(function(t){Ct.indexOf(t.key)>-1&&(t.preventDefault(),s(i))}),e,{passive:!1})}))}function s(e){t.State.is(F)&&n.go(e)}return{required:!!n,mount:function(){i(),o(),e&&(r(),t.on("refresh",(function(){setTimeout((function(){r(),n.emit("navigation:updated",t)}))})))},mounted:function(){e&&n.emit("navigation:mounted",t)}}},A11y:kt};var It=function(t){var n,e;function i(n,e){return t.call(this,n,e,zt)||this}return e=t,(n=i).prototype=Object.create(e.prototype),n.prototype.constructor=n,n.__proto__=e,i}(U);window.Splide=It}();

/* File: assets/js/custom/categories.js */
// Setting the animated dot for the categories tabs
// like in text_tabs block
var categoriesDots = function() {
	var tabs = event.target.parentElement;

	if(tabs.classList.contains('categories--dot') > -1) {
		var active_child = tabs.querySelector('.active');
		var dot = tabs.querySelector('.categories__dot');
		if(dot) {
			dot.style.left = (active_child.offsetLeft + Math.floor(active_child.offsetWidth / 2)) + 'px';
		}		
	}	
}

flash.ready(function(){
	flash.listen('.categories__items', 'flashTabsUpdated', function(event){
		var tabs = event.target.parentElement;
		
		if(tabs.classList.contains('categories--dot') > -1) {
			var active_child = tabs.querySelector('.active');
			var dot = tabs.querySelector('.categories__dot');
			if(dot) {
				dot.style.left = (active_child.offsetLeft + Math.floor(active_child.offsetWidth / 2)) + 'px';
			}		
		}
	});

	document.querySelectorAll('.categories--dot').forEach(function(element){
		// Adding the dot
		element.insertAdjacentHTML('beforeend', '<span class="categories__dot"></span>');

		// Triggering the update
		var flashReadyEvent = new Event('flashTabsUpdated');
		element.querySelector('.categories__items').dispatchEvent(flashReadyEvent);
	});
}, 'categories');

/* File: assets/js/custom/iframesBlocking.js */
/**
 *  LIBRARY FOR BLOCKING / UNBLOCKING IFRAMES BASED ON COOKIES SETTINGS
*/
function cookiesIframeBlocker(options) {
    var self = this;

    // Setting options
    self.allowed_categories = [];
    var defaults = {
        trigger_event: 'CookieScriptAccept',
        acceptedCategories: function() {
            return self.allowed_categories;
        },
        consentCallback: function(category) {
            self.allowed_categories.push(category);
            var ev = new Event('CookieScriptAccept');
            window.dispatchEvent(ev);
        }
    };
    self.options = Object.assign(defaults, options);

    // Init
    window.addEventListener(self.options.trigger_event, function(e) { 
        self.refresh();
    });
    self.refresh();
}

/**
 * Update the iframes on the page
 */
cookiesIframeBlocker.prototype.refresh = function() {
    this.block();
    this.unBlock();
}

/**
 * This function will return the list of accepted cookies categories
 */
cookiesIframeBlocker.prototype.acceptedCategories = function() {
    var self = this;

    // Return custom method for accepted categories
    if(self.options.acceptedCategories) {
        return self.options.acceptedCategories();
    }

    return ['functionality'];
}

/**
 * Check if a category is allowed or not
 * @param {string} category The name of the category to check
 * @return {bool} True or false depending if the category is allowed
 */
cookiesIframeBlocker.prototype.isAllowed = function(category) {
    var self = this;

    return self.acceptedCategories().indexOf(category) > -1;
}

/**
 * Unblock all the iframes that can be unblocked
 */
cookiesIframeBlocker.prototype.unBlock = function() {
    var self = this;

    self.options.acceptedCategories().forEach(function(category){
        var iframes = document.querySelectorAll('iframe[data-category="' + category + '"]');

        iframes.forEach(function(iframe){
            var block = iframe.parentNode.querySelector('.iframe-block');
            var src = iframe.getAttribute('data-src');
            iframe.setAttribute('src', src);
            if(block) {
                block.remove();
            }
        });
    });
}

/**
 * Block all the iframes that must be blocked
 */
cookiesIframeBlocker.prototype.block = function() {
    var self = this;

    document.querySelectorAll('iframe[data-category]').forEach(function(el){
        var parent =  el.parentNode;
        var category = el.getAttribute('data-category');
        if(!category) {
            category = 'functionality';
        }
        if(self.isAllowed(category) || parent.querySelector('.iframe-block')) {
            return;
        }

        var url = el.getAttribute('data-src') || el.getAttribute('src');
        if(el.getAttribute('src')) {
            el.setAttribute('data-src', url);
            el.removeAttribute('src');
        }

        if(url.indexOf('//') === 0) {
            url = 'https:' + url;
        }
        
        var domain = (new URL(url)).hostname;
        
        // Fix parent style
        var parent_style = window.getComputedStyle(parent);
        parent.style.position = parent_style.getPropertyValue('position');

        // Inject the code
        var html = '<div class="iframe-block" data-category="' + category + '">' +
            '<div class="iframe-block__inner">' +
                '<p class="iframe-block__intro">' + 
                    'This content is hosted by a third party [domain]. By showing the external content you accept the terms and conditions of [domain].' + 
                '</p>' +
                '<button class="iframe-block__button">Show External Content</button>' + 
                '<p class="iframe-block__note">*Your choice will be saved in a cookie managed by comsec.ie until you\'ve closed your browser.</p>' + 
            '</div>' +
        '</div>';
        html = html.replace(/\[domain\]/g, domain);
        parent.insertAdjacentHTML('beforeend', html);

        // Listen to the click on the consent button on the iframe
        parent.querySelector('.iframe-block__button').addEventListener('click', function(event){
            event.stopPropagation();
            self.allow(category);
        })
    });   
}

/**
 * Give consent for a specific category
 */
cookiesIframeBlocker.prototype.allow = function(category) {
    var self = this;

    if(self.options.consentCallback) {
        self.options.consentCallback(category);
    } else {
        console.log('You didn\'t set a custom allow method to handle consent action. WTF.');
    }
}

/* File: assets/js/custom/jquery.js */
/*! jQuery v3.5.1 | (c) JS Foundation and other contributors | jquery.org/license */
!function(e,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(C,e){"use strict";var t=[],r=Object.getPrototypeOf,s=t.slice,g=t.flat?function(e){return t.flat.call(e)}:function(e){return t.concat.apply([],e)},u=t.push,i=t.indexOf,n={},o=n.toString,v=n.hasOwnProperty,a=v.toString,l=a.call(Object),y={},m=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType},x=function(e){return null!=e&&e===e.window},E=C.document,c={type:!0,src:!0,nonce:!0,noModule:!0};function b(e,t,n){var r,i,o=(n=n||E).createElement("script");if(o.text=e,t)for(r in c)(i=t[r]||t.getAttribute&&t.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function w(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?n[o.call(e)]||"object":typeof e}var f="3.5.1",S=function(e,t){return new S.fn.init(e,t)};function p(e){var t=!!e&&"length"in e&&e.length,n=w(e);return!m(e)&&!x(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}S.fn=S.prototype={jquery:f,constructor:S,length:0,toArray:function(){return s.call(this)},get:function(e){return null==e?s.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=S.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return S.each(this,e)},map:function(n){return this.pushStack(S.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(s.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(S.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(S.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(0<=n&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:u,sort:t.sort,splice:t.splice},S.extend=S.fn.extend=function(){var e,t,n,r,i,o,a=arguments[0]||{},s=1,u=arguments.length,l=!1;for("boolean"==typeof a&&(l=a,a=arguments[s]||{},s++),"object"==typeof a||m(a)||(a={}),s===u&&(a=this,s--);s<u;s++)if(null!=(e=arguments[s]))for(t in e)r=e[t],"__proto__"!==t&&a!==r&&(l&&r&&(S.isPlainObject(r)||(i=Array.isArray(r)))?(n=a[t],o=i&&!Array.isArray(n)?[]:i||S.isPlainObject(n)?n:{},i=!1,a[t]=S.extend(l,o,r)):void 0!==r&&(a[t]=r));return a},S.extend({expando:"jQuery"+(f+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==o.call(e))&&(!(t=r(e))||"function"==typeof(n=v.call(t,"constructor")&&t.constructor)&&a.call(n)===l)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,n){b(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(p(e)){for(n=e.length;r<n;r++)if(!1===t.call(e[r],r,e[r]))break}else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},makeArray:function(e,t){var n=t||[];return null!=e&&(p(Object(e))?S.merge(n,"string"==typeof e?[e]:e):u.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:i.call(t,e,n)},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,a=!n;i<o;i++)!t(e[i],i)!==a&&r.push(e[i]);return r},map:function(e,t,n){var r,i,o=0,a=[];if(p(e))for(r=e.length;o<r;o++)null!=(i=t(e[o],o,n))&&a.push(i);else for(o in e)null!=(i=t(e[o],o,n))&&a.push(i);return g(a)},guid:1,support:y}),"function"==typeof Symbol&&(S.fn[Symbol.iterator]=t[Symbol.iterator]),S.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){n["[object "+t+"]"]=t.toLowerCase()});var d=function(n){var e,d,b,o,i,h,f,g,w,u,l,T,C,a,E,v,s,c,y,S="sizzle"+1*new Date,p=n.document,k=0,r=0,m=ue(),x=ue(),A=ue(),N=ue(),D=function(e,t){return e===t&&(l=!0),0},j={}.hasOwnProperty,t=[],q=t.pop,L=t.push,H=t.push,O=t.slice,P=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},R="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",M="[\\x20\\t\\r\\n\\f]",I="(?:\\\\[\\da-fA-F]{1,6}"+M+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",W="\\["+M+"*("+I+")(?:"+M+"*([*^$|!~]?=)"+M+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+I+"))|)"+M+"*\\]",F=":("+I+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+W+")*)|.*)\\)|)",B=new RegExp(M+"+","g"),$=new RegExp("^"+M+"+|((?:^|[^\\\\])(?:\\\\.)*)"+M+"+$","g"),_=new RegExp("^"+M+"*,"+M+"*"),z=new RegExp("^"+M+"*([>+~]|"+M+")"+M+"*"),U=new RegExp(M+"|>"),X=new RegExp(F),V=new RegExp("^"+I+"$"),G={ID:new RegExp("^#("+I+")"),CLASS:new RegExp("^\\.("+I+")"),TAG:new RegExp("^("+I+"|[*])"),ATTR:new RegExp("^"+W),PSEUDO:new RegExp("^"+F),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+M+"*(even|odd|(([+-]|)(\\d*)n|)"+M+"*(?:([+-]|)"+M+"*(\\d+)|))"+M+"*\\)|)","i"),bool:new RegExp("^(?:"+R+")$","i"),needsContext:new RegExp("^"+M+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+M+"*((?:-\\d)?\\d*)"+M+"*\\)|)(?=[^-]|$)","i")},Y=/HTML$/i,Q=/^(?:input|select|textarea|button)$/i,J=/^h\d$/i,K=/^[^{]+\{\s*\[native \w/,Z=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ee=/[+~]/,te=new RegExp("\\\\[\\da-fA-F]{1,6}"+M+"?|\\\\([^\\r\\n\\f])","g"),ne=function(e,t){var n="0x"+e.slice(1)-65536;return t||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},re=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,ie=function(e,t){return t?"\0"===e?"\ufffd":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},oe=function(){T()},ae=be(function(e){return!0===e.disabled&&"fieldset"===e.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{H.apply(t=O.call(p.childNodes),p.childNodes),t[p.childNodes.length].nodeType}catch(e){H={apply:t.length?function(e,t){L.apply(e,O.call(t))}:function(e,t){var n=e.length,r=0;while(e[n++]=t[r++]);e.length=n-1}}}function se(t,e,n,r){var i,o,a,s,u,l,c,f=e&&e.ownerDocument,p=e?e.nodeType:9;if(n=n||[],"string"!=typeof t||!t||1!==p&&9!==p&&11!==p)return n;if(!r&&(T(e),e=e||C,E)){if(11!==p&&(u=Z.exec(t)))if(i=u[1]){if(9===p){if(!(a=e.getElementById(i)))return n;if(a.id===i)return n.push(a),n}else if(f&&(a=f.getElementById(i))&&y(e,a)&&a.id===i)return n.push(a),n}else{if(u[2])return H.apply(n,e.getElementsByTagName(t)),n;if((i=u[3])&&d.getElementsByClassName&&e.getElementsByClassName)return H.apply(n,e.getElementsByClassName(i)),n}if(d.qsa&&!N[t+" "]&&(!v||!v.test(t))&&(1!==p||"object"!==e.nodeName.toLowerCase())){if(c=t,f=e,1===p&&(U.test(t)||z.test(t))){(f=ee.test(t)&&ye(e.parentNode)||e)===e&&d.scope||((s=e.getAttribute("id"))?s=s.replace(re,ie):e.setAttribute("id",s=S)),o=(l=h(t)).length;while(o--)l[o]=(s?"#"+s:":scope")+" "+xe(l[o]);c=l.join(",")}try{return H.apply(n,f.querySelectorAll(c)),n}catch(e){N(t,!0)}finally{s===S&&e.removeAttribute("id")}}}return g(t.replace($,"$1"),e,n,r)}function ue(){var r=[];return function e(t,n){return r.push(t+" ")>b.cacheLength&&delete e[r.shift()],e[t+" "]=n}}function le(e){return e[S]=!0,e}function ce(e){var t=C.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function fe(e,t){var n=e.split("|"),r=n.length;while(r--)b.attrHandle[n[r]]=t}function pe(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(r)return r;if(n)while(n=n.nextSibling)if(n===t)return-1;return e?1:-1}function de(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}function he(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}function ge(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&ae(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function ve(a){return le(function(o){return o=+o,le(function(e,t){var n,r=a([],e.length,o),i=r.length;while(i--)e[n=r[i]]&&(e[n]=!(t[n]=e[n]))})})}function ye(e){return e&&"undefined"!=typeof e.getElementsByTagName&&e}for(e in d=se.support={},i=se.isXML=function(e){var t=e.namespaceURI,n=(e.ownerDocument||e).documentElement;return!Y.test(t||n&&n.nodeName||"HTML")},T=se.setDocument=function(e){var t,n,r=e?e.ownerDocument||e:p;return r!=C&&9===r.nodeType&&r.documentElement&&(a=(C=r).documentElement,E=!i(C),p!=C&&(n=C.defaultView)&&n.top!==n&&(n.addEventListener?n.addEventListener("unload",oe,!1):n.attachEvent&&n.attachEvent("onunload",oe)),d.scope=ce(function(e){return a.appendChild(e).appendChild(C.createElement("div")),"undefined"!=typeof e.querySelectorAll&&!e.querySelectorAll(":scope fieldset div").length}),d.attributes=ce(function(e){return e.className="i",!e.getAttribute("className")}),d.getElementsByTagName=ce(function(e){return e.appendChild(C.createComment("")),!e.getElementsByTagName("*").length}),d.getElementsByClassName=K.test(C.getElementsByClassName),d.getById=ce(function(e){return a.appendChild(e).id=S,!C.getElementsByName||!C.getElementsByName(S).length}),d.getById?(b.filter.ID=function(e){var t=e.replace(te,ne);return function(e){return e.getAttribute("id")===t}},b.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&E){var n=t.getElementById(e);return n?[n]:[]}}):(b.filter.ID=function(e){var n=e.replace(te,ne);return function(e){var t="undefined"!=typeof e.getAttributeNode&&e.getAttributeNode("id");return t&&t.value===n}},b.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&E){var n,r,i,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];i=t.getElementsByName(e),r=0;while(o=i[r++])if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),b.find.TAG=d.getElementsByTagName?function(e,t){return"undefined"!=typeof t.getElementsByTagName?t.getElementsByTagName(e):d.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,r=[],i=0,o=t.getElementsByTagName(e);if("*"===e){while(n=o[i++])1===n.nodeType&&r.push(n);return r}return o},b.find.CLASS=d.getElementsByClassName&&function(e,t){if("undefined"!=typeof t.getElementsByClassName&&E)return t.getElementsByClassName(e)},s=[],v=[],(d.qsa=K.test(C.querySelectorAll))&&(ce(function(e){var t;a.appendChild(e).innerHTML="<a id='"+S+"'></a><select id='"+S+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&v.push("[*^$]="+M+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||v.push("\\["+M+"*(?:value|"+R+")"),e.querySelectorAll("[id~="+S+"-]").length||v.push("~="),(t=C.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||v.push("\\["+M+"*name"+M+"*="+M+"*(?:''|\"\")"),e.querySelectorAll(":checked").length||v.push(":checked"),e.querySelectorAll("a#"+S+"+*").length||v.push(".#.+[+~]"),e.querySelectorAll("\\\f"),v.push("[\\r\\n\\f]")}),ce(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=C.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&v.push("name"+M+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&v.push(":enabled",":disabled"),a.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&v.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),v.push(",.*:")})),(d.matchesSelector=K.test(c=a.matches||a.webkitMatchesSelector||a.mozMatchesSelector||a.oMatchesSelector||a.msMatchesSelector))&&ce(function(e){d.disconnectedMatch=c.call(e,"*"),c.call(e,"[s!='']:x"),s.push("!=",F)}),v=v.length&&new RegExp(v.join("|")),s=s.length&&new RegExp(s.join("|")),t=K.test(a.compareDocumentPosition),y=t||K.test(a.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)while(t=t.parentNode)if(t===e)return!0;return!1},D=t?function(e,t){if(e===t)return l=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!d.sortDetached&&t.compareDocumentPosition(e)===n?e==C||e.ownerDocument==p&&y(p,e)?-1:t==C||t.ownerDocument==p&&y(p,t)?1:u?P(u,e)-P(u,t):0:4&n?-1:1)}:function(e,t){if(e===t)return l=!0,0;var n,r=0,i=e.parentNode,o=t.parentNode,a=[e],s=[t];if(!i||!o)return e==C?-1:t==C?1:i?-1:o?1:u?P(u,e)-P(u,t):0;if(i===o)return pe(e,t);n=e;while(n=n.parentNode)a.unshift(n);n=t;while(n=n.parentNode)s.unshift(n);while(a[r]===s[r])r++;return r?pe(a[r],s[r]):a[r]==p?-1:s[r]==p?1:0}),C},se.matches=function(e,t){return se(e,null,null,t)},se.matchesSelector=function(e,t){if(T(e),d.matchesSelector&&E&&!N[t+" "]&&(!s||!s.test(t))&&(!v||!v.test(t)))try{var n=c.call(e,t);if(n||d.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){N(t,!0)}return 0<se(t,C,null,[e]).length},se.contains=function(e,t){return(e.ownerDocument||e)!=C&&T(e),y(e,t)},se.attr=function(e,t){(e.ownerDocument||e)!=C&&T(e);var n=b.attrHandle[t.toLowerCase()],r=n&&j.call(b.attrHandle,t.toLowerCase())?n(e,t,!E):void 0;return void 0!==r?r:d.attributes||!E?e.getAttribute(t):(r=e.getAttributeNode(t))&&r.specified?r.value:null},se.escape=function(e){return(e+"").replace(re,ie)},se.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},se.uniqueSort=function(e){var t,n=[],r=0,i=0;if(l=!d.detectDuplicates,u=!d.sortStable&&e.slice(0),e.sort(D),l){while(t=e[i++])t===e[i]&&(r=n.push(i));while(r--)e.splice(n[r],1)}return u=null,e},o=se.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===i||4===i)return e.nodeValue}else while(t=e[r++])n+=o(t);return n},(b=se.selectors={cacheLength:50,createPseudo:le,match:G,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(te,ne),e[3]=(e[3]||e[4]||e[5]||"").replace(te,ne),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||se.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&se.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return G.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&X.test(n)&&(t=h(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(te,ne).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=m[e+" "];return t||(t=new RegExp("(^|"+M+")"+e+"("+M+"|$)"))&&m(e,function(e){return t.test("string"==typeof e.className&&e.className||"undefined"!=typeof e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(n,r,i){return function(e){var t=se.attr(e,n);return null==t?"!="===r:!r||(t+="","="===r?t===i:"!="===r?t!==i:"^="===r?i&&0===t.indexOf(i):"*="===r?i&&-1<t.indexOf(i):"$="===r?i&&t.slice(-i.length)===i:"~="===r?-1<(" "+t.replace(B," ")+" ").indexOf(i):"|="===r&&(t===i||t.slice(0,i.length+1)===i+"-"))}},CHILD:function(h,e,t,g,v){var y="nth"!==h.slice(0,3),m="last"!==h.slice(-4),x="of-type"===e;return 1===g&&0===v?function(e){return!!e.parentNode}:function(e,t,n){var r,i,o,a,s,u,l=y!==m?"nextSibling":"previousSibling",c=e.parentNode,f=x&&e.nodeName.toLowerCase(),p=!n&&!x,d=!1;if(c){if(y){while(l){a=e;while(a=a[l])if(x?a.nodeName.toLowerCase()===f:1===a.nodeType)return!1;u=l="only"===h&&!u&&"nextSibling"}return!0}if(u=[m?c.firstChild:c.lastChild],m&&p){d=(s=(r=(i=(o=(a=c)[S]||(a[S]={}))[a.uniqueID]||(o[a.uniqueID]={}))[h]||[])[0]===k&&r[1])&&r[2],a=s&&c.childNodes[s];while(a=++s&&a&&a[l]||(d=s=0)||u.pop())if(1===a.nodeType&&++d&&a===e){i[h]=[k,s,d];break}}else if(p&&(d=s=(r=(i=(o=(a=e)[S]||(a[S]={}))[a.uniqueID]||(o[a.uniqueID]={}))[h]||[])[0]===k&&r[1]),!1===d)while(a=++s&&a&&a[l]||(d=s=0)||u.pop())if((x?a.nodeName.toLowerCase()===f:1===a.nodeType)&&++d&&(p&&((i=(o=a[S]||(a[S]={}))[a.uniqueID]||(o[a.uniqueID]={}))[h]=[k,d]),a===e))break;return(d-=v)===g||d%g==0&&0<=d/g}}},PSEUDO:function(e,o){var t,a=b.pseudos[e]||b.setFilters[e.toLowerCase()]||se.error("unsupported pseudo: "+e);return a[S]?a(o):1<a.length?(t=[e,e,"",o],b.setFilters.hasOwnProperty(e.toLowerCase())?le(function(e,t){var n,r=a(e,o),i=r.length;while(i--)e[n=P(e,r[i])]=!(t[n]=r[i])}):function(e){return a(e,0,t)}):a}},pseudos:{not:le(function(e){var r=[],i=[],s=f(e.replace($,"$1"));return s[S]?le(function(e,t,n,r){var i,o=s(e,null,r,[]),a=e.length;while(a--)(i=o[a])&&(e[a]=!(t[a]=i))}):function(e,t,n){return r[0]=e,s(r,null,n,i),r[0]=null,!i.pop()}}),has:le(function(t){return function(e){return 0<se(t,e).length}}),contains:le(function(t){return t=t.replace(te,ne),function(e){return-1<(e.textContent||o(e)).indexOf(t)}}),lang:le(function(n){return V.test(n||"")||se.error("unsupported lang: "+n),n=n.replace(te,ne).toLowerCase(),function(e){var t;do{if(t=E?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=n.location&&n.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===a},focus:function(e){return e===C.activeElement&&(!C.hasFocus||C.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:ge(!1),disabled:ge(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!b.pseudos.empty(e)},header:function(e){return J.test(e.nodeName)},input:function(e){return Q.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:ve(function(){return[0]}),last:ve(function(e,t){return[t-1]}),eq:ve(function(e,t,n){return[n<0?n+t:n]}),even:ve(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:ve(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:ve(function(e,t,n){for(var r=n<0?n+t:t<n?t:n;0<=--r;)e.push(r);return e}),gt:ve(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=b.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})b.pseudos[e]=de(e);for(e in{submit:!0,reset:!0})b.pseudos[e]=he(e);function me(){}function xe(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function be(s,e,t){var u=e.dir,l=e.next,c=l||u,f=t&&"parentNode"===c,p=r++;return e.first?function(e,t,n){while(e=e[u])if(1===e.nodeType||f)return s(e,t,n);return!1}:function(e,t,n){var r,i,o,a=[k,p];if(n){while(e=e[u])if((1===e.nodeType||f)&&s(e,t,n))return!0}else while(e=e[u])if(1===e.nodeType||f)if(i=(o=e[S]||(e[S]={}))[e.uniqueID]||(o[e.uniqueID]={}),l&&l===e.nodeName.toLowerCase())e=e[u]||e;else{if((r=i[c])&&r[0]===k&&r[1]===p)return a[2]=r[2];if((i[c]=a)[2]=s(e,t,n))return!0}return!1}}function we(i){return 1<i.length?function(e,t,n){var r=i.length;while(r--)if(!i[r](e,t,n))return!1;return!0}:i[0]}function Te(e,t,n,r,i){for(var o,a=[],s=0,u=e.length,l=null!=t;s<u;s++)(o=e[s])&&(n&&!n(o,r,i)||(a.push(o),l&&t.push(s)));return a}function Ce(d,h,g,v,y,e){return v&&!v[S]&&(v=Ce(v)),y&&!y[S]&&(y=Ce(y,e)),le(function(e,t,n,r){var i,o,a,s=[],u=[],l=t.length,c=e||function(e,t,n){for(var r=0,i=t.length;r<i;r++)se(e,t[r],n);return n}(h||"*",n.nodeType?[n]:n,[]),f=!d||!e&&h?c:Te(c,s,d,n,r),p=g?y||(e?d:l||v)?[]:t:f;if(g&&g(f,p,n,r),v){i=Te(p,u),v(i,[],n,r),o=i.length;while(o--)(a=i[o])&&(p[u[o]]=!(f[u[o]]=a))}if(e){if(y||d){if(y){i=[],o=p.length;while(o--)(a=p[o])&&i.push(f[o]=a);y(null,p=[],i,r)}o=p.length;while(o--)(a=p[o])&&-1<(i=y?P(e,a):s[o])&&(e[i]=!(t[i]=a))}}else p=Te(p===t?p.splice(l,p.length):p),y?y(null,t,p,r):H.apply(t,p)})}function Ee(e){for(var i,t,n,r=e.length,o=b.relative[e[0].type],a=o||b.relative[" "],s=o?1:0,u=be(function(e){return e===i},a,!0),l=be(function(e){return-1<P(i,e)},a,!0),c=[function(e,t,n){var r=!o&&(n||t!==w)||((i=t).nodeType?u(e,t,n):l(e,t,n));return i=null,r}];s<r;s++)if(t=b.relative[e[s].type])c=[be(we(c),t)];else{if((t=b.filter[e[s].type].apply(null,e[s].matches))[S]){for(n=++s;n<r;n++)if(b.relative[e[n].type])break;return Ce(1<s&&we(c),1<s&&xe(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace($,"$1"),t,s<n&&Ee(e.slice(s,n)),n<r&&Ee(e=e.slice(n)),n<r&&xe(e))}c.push(t)}return we(c)}return me.prototype=b.filters=b.pseudos,b.setFilters=new me,h=se.tokenize=function(e,t){var n,r,i,o,a,s,u,l=x[e+" "];if(l)return t?0:l.slice(0);a=e,s=[],u=b.preFilter;while(a){for(o in n&&!(r=_.exec(a))||(r&&(a=a.slice(r[0].length)||a),s.push(i=[])),n=!1,(r=z.exec(a))&&(n=r.shift(),i.push({value:n,type:r[0].replace($," ")}),a=a.slice(n.length)),b.filter)!(r=G[o].exec(a))||u[o]&&!(r=u[o](r))||(n=r.shift(),i.push({value:n,type:o,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?se.error(e):x(e,s).slice(0)},f=se.compile=function(e,t){var n,v,y,m,x,r,i=[],o=[],a=A[e+" "];if(!a){t||(t=h(e)),n=t.length;while(n--)(a=Ee(t[n]))[S]?i.push(a):o.push(a);(a=A(e,(v=o,m=0<(y=i).length,x=0<v.length,r=function(e,t,n,r,i){var o,a,s,u=0,l="0",c=e&&[],f=[],p=w,d=e||x&&b.find.TAG("*",i),h=k+=null==p?1:Math.random()||.1,g=d.length;for(i&&(w=t==C||t||i);l!==g&&null!=(o=d[l]);l++){if(x&&o){a=0,t||o.ownerDocument==C||(T(o),n=!E);while(s=v[a++])if(s(o,t||C,n)){r.push(o);break}i&&(k=h)}m&&((o=!s&&o)&&u--,e&&c.push(o))}if(u+=l,m&&l!==u){a=0;while(s=y[a++])s(c,f,t,n);if(e){if(0<u)while(l--)c[l]||f[l]||(f[l]=q.call(r));f=Te(f)}H.apply(r,f),i&&!e&&0<f.length&&1<u+y.length&&se.uniqueSort(r)}return i&&(k=h,w=p),c},m?le(r):r))).selector=e}return a},g=se.select=function(e,t,n,r){var i,o,a,s,u,l="function"==typeof e&&e,c=!r&&h(e=l.selector||e);if(n=n||[],1===c.length){if(2<(o=c[0]=c[0].slice(0)).length&&"ID"===(a=o[0]).type&&9===t.nodeType&&E&&b.relative[o[1].type]){if(!(t=(b.find.ID(a.matches[0].replace(te,ne),t)||[])[0]))return n;l&&(t=t.parentNode),e=e.slice(o.shift().value.length)}i=G.needsContext.test(e)?0:o.length;while(i--){if(a=o[i],b.relative[s=a.type])break;if((u=b.find[s])&&(r=u(a.matches[0].replace(te,ne),ee.test(o[0].type)&&ye(t.parentNode)||t))){if(o.splice(i,1),!(e=r.length&&xe(o)))return H.apply(n,r),n;break}}}return(l||f(e,c))(r,t,!E,n,!t||ee.test(e)&&ye(t.parentNode)||t),n},d.sortStable=S.split("").sort(D).join("")===S,d.detectDuplicates=!!l,T(),d.sortDetached=ce(function(e){return 1&e.compareDocumentPosition(C.createElement("fieldset"))}),ce(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||fe("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),d.attributes&&ce(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||fe("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),ce(function(e){return null==e.getAttribute("disabled")})||fe(R,function(e,t,n){var r;if(!n)return!0===e[t]?t.toLowerCase():(r=e.getAttributeNode(t))&&r.specified?r.value:null}),se}(C);S.find=d,S.expr=d.selectors,S.expr[":"]=S.expr.pseudos,S.uniqueSort=S.unique=d.uniqueSort,S.text=d.getText,S.isXMLDoc=d.isXML,S.contains=d.contains,S.escapeSelector=d.escape;var h=function(e,t,n){var r=[],i=void 0!==n;while((e=e[t])&&9!==e.nodeType)if(1===e.nodeType){if(i&&S(e).is(n))break;r.push(e)}return r},T=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},k=S.expr.match.needsContext;function A(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var N=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function D(e,n,r){return m(n)?S.grep(e,function(e,t){return!!n.call(e,t,e)!==r}):n.nodeType?S.grep(e,function(e){return e===n!==r}):"string"!=typeof n?S.grep(e,function(e){return-1<i.call(n,e)!==r}):S.filter(n,e,r)}S.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?S.find.matchesSelector(r,e)?[r]:[]:S.find.matches(e,S.grep(t,function(e){return 1===e.nodeType}))},S.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(S(e).filter(function(){for(t=0;t<r;t++)if(S.contains(i[t],this))return!0}));for(n=this.pushStack([]),t=0;t<r;t++)S.find(e,i[t],n);return 1<r?S.uniqueSort(n):n},filter:function(e){return this.pushStack(D(this,e||[],!1))},not:function(e){return this.pushStack(D(this,e||[],!0))},is:function(e){return!!D(this,"string"==typeof e&&k.test(e)?S(e):e||[],!1).length}});var j,q=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(S.fn.init=function(e,t,n){var r,i;if(!e)return this;if(n=n||j,"string"==typeof e){if(!(r="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:q.exec(e))||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof S?t[0]:t,S.merge(this,S.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:E,!0)),N.test(r[1])&&S.isPlainObject(t))for(r in t)m(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return(i=E.getElementById(r[2]))&&(this[0]=i,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):m(e)?void 0!==n.ready?n.ready(e):e(S):S.makeArray(e,this)}).prototype=S.fn,j=S(E);var L=/^(?:parents|prev(?:Until|All))/,H={children:!0,contents:!0,next:!0,prev:!0};function O(e,t){while((e=e[t])&&1!==e.nodeType);return e}S.fn.extend({has:function(e){var t=S(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(S.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,i=this.length,o=[],a="string"!=typeof e&&S(e);if(!k.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?-1<a.index(n):1===n.nodeType&&S.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?S.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?i.call(S(e),this[0]):i.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(S.uniqueSort(S.merge(this.get(),S(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),S.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return h(e,"parentNode")},parentsUntil:function(e,t,n){return h(e,"parentNode",n)},next:function(e){return O(e,"nextSibling")},prev:function(e){return O(e,"previousSibling")},nextAll:function(e){return h(e,"nextSibling")},prevAll:function(e){return h(e,"previousSibling")},nextUntil:function(e,t,n){return h(e,"nextSibling",n)},prevUntil:function(e,t,n){return h(e,"previousSibling",n)},siblings:function(e){return T((e.parentNode||{}).firstChild,e)},children:function(e){return T(e.firstChild)},contents:function(e){return null!=e.contentDocument&&r(e.contentDocument)?e.contentDocument:(A(e,"template")&&(e=e.content||e),S.merge([],e.childNodes))}},function(r,i){S.fn[r]=function(e,t){var n=S.map(this,i,e);return"Until"!==r.slice(-5)&&(t=e),t&&"string"==typeof t&&(n=S.filter(t,n)),1<this.length&&(H[r]||S.uniqueSort(n),L.test(r)&&n.reverse()),this.pushStack(n)}});var P=/[^\x20\t\r\n\f]+/g;function R(e){return e}function M(e){throw e}function I(e,t,n,r){var i;try{e&&m(i=e.promise)?i.call(e).done(t).fail(n):e&&m(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}S.Callbacks=function(r){var e,n;r="string"==typeof r?(e=r,n={},S.each(e.match(P)||[],function(e,t){n[t]=!0}),n):S.extend({},r);var i,t,o,a,s=[],u=[],l=-1,c=function(){for(a=a||r.once,o=i=!0;u.length;l=-1){t=u.shift();while(++l<s.length)!1===s[l].apply(t[0],t[1])&&r.stopOnFalse&&(l=s.length,t=!1)}r.memory||(t=!1),i=!1,a&&(s=t?[]:"")},f={add:function(){return s&&(t&&!i&&(l=s.length-1,u.push(t)),function n(e){S.each(e,function(e,t){m(t)?r.unique&&f.has(t)||s.push(t):t&&t.length&&"string"!==w(t)&&n(t)})}(arguments),t&&!i&&c()),this},remove:function(){return S.each(arguments,function(e,t){var n;while(-1<(n=S.inArray(t,s,n)))s.splice(n,1),n<=l&&l--}),this},has:function(e){return e?-1<S.inArray(e,s):0<s.length},empty:function(){return s&&(s=[]),this},disable:function(){return a=u=[],s=t="",this},disabled:function(){return!s},lock:function(){return a=u=[],t||i||(s=t=""),this},locked:function(){return!!a},fireWith:function(e,t){return a||(t=[e,(t=t||[]).slice?t.slice():t],u.push(t),i||c()),this},fire:function(){return f.fireWith(this,arguments),this},fired:function(){return!!o}};return f},S.extend({Deferred:function(e){var o=[["notify","progress",S.Callbacks("memory"),S.Callbacks("memory"),2],["resolve","done",S.Callbacks("once memory"),S.Callbacks("once memory"),0,"resolved"],["reject","fail",S.Callbacks("once memory"),S.Callbacks("once memory"),1,"rejected"]],i="pending",a={state:function(){return i},always:function(){return s.done(arguments).fail(arguments),this},"catch":function(e){return a.then(null,e)},pipe:function(){var i=arguments;return S.Deferred(function(r){S.each(o,function(e,t){var n=m(i[t[4]])&&i[t[4]];s[t[1]](function(){var e=n&&n.apply(this,arguments);e&&m(e.promise)?e.promise().progress(r.notify).done(r.resolve).fail(r.reject):r[t[0]+"With"](this,n?[e]:arguments)})}),i=null}).promise()},then:function(t,n,r){var u=0;function l(i,o,a,s){return function(){var n=this,r=arguments,e=function(){var e,t;if(!(i<u)){if((e=a.apply(n,r))===o.promise())throw new TypeError("Thenable self-resolution");t=e&&("object"==typeof e||"function"==typeof e)&&e.then,m(t)?s?t.call(e,l(u,o,R,s),l(u,o,M,s)):(u++,t.call(e,l(u,o,R,s),l(u,o,M,s),l(u,o,R,o.notifyWith))):(a!==R&&(n=void 0,r=[e]),(s||o.resolveWith)(n,r))}},t=s?e:function(){try{e()}catch(e){S.Deferred.exceptionHook&&S.Deferred.exceptionHook(e,t.stackTrace),u<=i+1&&(a!==M&&(n=void 0,r=[e]),o.rejectWith(n,r))}};i?t():(S.Deferred.getStackHook&&(t.stackTrace=S.Deferred.getStackHook()),C.setTimeout(t))}}return S.Deferred(function(e){o[0][3].add(l(0,e,m(r)?r:R,e.notifyWith)),o[1][3].add(l(0,e,m(t)?t:R)),o[2][3].add(l(0,e,m(n)?n:M))}).promise()},promise:function(e){return null!=e?S.extend(e,a):a}},s={};return S.each(o,function(e,t){var n=t[2],r=t[5];a[t[1]]=n.add,r&&n.add(function(){i=r},o[3-e][2].disable,o[3-e][3].disable,o[0][2].lock,o[0][3].lock),n.add(t[3].fire),s[t[0]]=function(){return s[t[0]+"With"](this===s?void 0:this,arguments),this},s[t[0]+"With"]=n.fireWith}),a.promise(s),e&&e.call(s,s),s},when:function(e){var n=arguments.length,t=n,r=Array(t),i=s.call(arguments),o=S.Deferred(),a=function(t){return function(e){r[t]=this,i[t]=1<arguments.length?s.call(arguments):e,--n||o.resolveWith(r,i)}};if(n<=1&&(I(e,o.done(a(t)).resolve,o.reject,!n),"pending"===o.state()||m(i[t]&&i[t].then)))return o.then();while(t--)I(i[t],a(t),o.reject);return o.promise()}});var W=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;S.Deferred.exceptionHook=function(e,t){C.console&&C.console.warn&&e&&W.test(e.name)&&C.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},S.readyException=function(e){C.setTimeout(function(){throw e})};var F=S.Deferred();function B(){E.removeEventListener("DOMContentLoaded",B),C.removeEventListener("load",B),S.ready()}S.fn.ready=function(e){return F.then(e)["catch"](function(e){S.readyException(e)}),this},S.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--S.readyWait:S.isReady)||(S.isReady=!0)!==e&&0<--S.readyWait||F.resolveWith(E,[S])}}),S.ready.then=F.then,"complete"===E.readyState||"loading"!==E.readyState&&!E.documentElement.doScroll?C.setTimeout(S.ready):(E.addEventListener("DOMContentLoaded",B),C.addEventListener("load",B));var $=function(e,t,n,r,i,o,a){var s=0,u=e.length,l=null==n;if("object"===w(n))for(s in i=!0,n)$(e,t,s,n[s],!0,o,a);else if(void 0!==r&&(i=!0,m(r)||(a=!0),l&&(a?(t.call(e,r),t=null):(l=t,t=function(e,t,n){return l.call(S(e),n)})),t))for(;s<u;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:l?t.call(e):u?t(e[0],n):o},_=/^-ms-/,z=/-([a-z])/g;function U(e,t){return t.toUpperCase()}function X(e){return e.replace(_,"ms-").replace(z,U)}var V=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function G(){this.expando=S.expando+G.uid++}G.uid=1,G.prototype={cache:function(e){var t=e[this.expando];return t||(t={},V(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[X(t)]=n;else for(r in t)i[X(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][X(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(t=Array.isArray(t)?t.map(X):(t=X(t))in r?[t]:t.match(P)||[]).length;while(n--)delete r[t[n]]}(void 0===t||S.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!S.isEmptyObject(t)}};var Y=new G,Q=new G,J=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,K=/[A-Z]/g;function Z(e,t,n){var r,i;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(K,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n="true"===(i=n)||"false"!==i&&("null"===i?null:i===+i+""?+i:J.test(i)?JSON.parse(i):i)}catch(e){}Q.set(e,t,n)}else n=void 0;return n}S.extend({hasData:function(e){return Q.hasData(e)||Y.hasData(e)},data:function(e,t,n){return Q.access(e,t,n)},removeData:function(e,t){Q.remove(e,t)},_data:function(e,t,n){return Y.access(e,t,n)},_removeData:function(e,t){Y.remove(e,t)}}),S.fn.extend({data:function(n,e){var t,r,i,o=this[0],a=o&&o.attributes;if(void 0===n){if(this.length&&(i=Q.get(o),1===o.nodeType&&!Y.get(o,"hasDataAttrs"))){t=a.length;while(t--)a[t]&&0===(r=a[t].name).indexOf("data-")&&(r=X(r.slice(5)),Z(o,r,i[r]));Y.set(o,"hasDataAttrs",!0)}return i}return"object"==typeof n?this.each(function(){Q.set(this,n)}):$(this,function(e){var t;if(o&&void 0===e)return void 0!==(t=Q.get(o,n))?t:void 0!==(t=Z(o,n))?t:void 0;this.each(function(){Q.set(this,n,e)})},null,e,1<arguments.length,null,!0)},removeData:function(e){return this.each(function(){Q.remove(this,e)})}}),S.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=Y.get(e,t),n&&(!r||Array.isArray(n)?r=Y.access(e,t,S.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=S.queue(e,t),r=n.length,i=n.shift(),o=S._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,function(){S.dequeue(e,t)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return Y.get(e,n)||Y.access(e,n,{empty:S.Callbacks("once memory").add(function(){Y.remove(e,[t+"queue",n])})})}}),S.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?S.queue(this[0],t):void 0===n?this:this.each(function(){var e=S.queue(this,t,n);S._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&S.dequeue(this,t)})},dequeue:function(e){return this.each(function(){S.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=S.Deferred(),o=this,a=this.length,s=function(){--r||i.resolveWith(o,[o])};"string"!=typeof e&&(t=e,e=void 0),e=e||"fx";while(a--)(n=Y.get(o[a],e+"queueHooks"))&&n.empty&&(r++,n.empty.add(s));return s(),i.promise(t)}});var ee=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,te=new RegExp("^(?:([+-])=|)("+ee+")([a-z%]*)$","i"),ne=["Top","Right","Bottom","Left"],re=E.documentElement,ie=function(e){return S.contains(e.ownerDocument,e)},oe={composed:!0};re.getRootNode&&(ie=function(e){return S.contains(e.ownerDocument,e)||e.getRootNode(oe)===e.ownerDocument});var ae=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&ie(e)&&"none"===S.css(e,"display")};function se(e,t,n,r){var i,o,a=20,s=r?function(){return r.cur()}:function(){return S.css(e,t,"")},u=s(),l=n&&n[3]||(S.cssNumber[t]?"":"px"),c=e.nodeType&&(S.cssNumber[t]||"px"!==l&&+u)&&te.exec(S.css(e,t));if(c&&c[3]!==l){u/=2,l=l||c[3],c=+u||1;while(a--)S.style(e,t,c+l),(1-o)*(1-(o=s()/u||.5))<=0&&(a=0),c/=o;c*=2,S.style(e,t,c+l),n=n||[]}return n&&(c=+c||+u||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=l,r.start=c,r.end=i)),i}var ue={};function le(e,t){for(var n,r,i,o,a,s,u,l=[],c=0,f=e.length;c<f;c++)(r=e[c]).style&&(n=r.style.display,t?("none"===n&&(l[c]=Y.get(r,"display")||null,l[c]||(r.style.display="")),""===r.style.display&&ae(r)&&(l[c]=(u=a=o=void 0,a=(i=r).ownerDocument,s=i.nodeName,(u=ue[s])||(o=a.body.appendChild(a.createElement(s)),u=S.css(o,"display"),o.parentNode.removeChild(o),"none"===u&&(u="block"),ue[s]=u)))):"none"!==n&&(l[c]="none",Y.set(r,"display",n)));for(c=0;c<f;c++)null!=l[c]&&(e[c].style.display=l[c]);return e}S.fn.extend({show:function(){return le(this,!0)},hide:function(){return le(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){ae(this)?S(this).show():S(this).hide()})}});var ce,fe,pe=/^(?:checkbox|radio)$/i,de=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,he=/^$|^module$|\/(?:java|ecma)script/i;ce=E.createDocumentFragment().appendChild(E.createElement("div")),(fe=E.createElement("input")).setAttribute("type","radio"),fe.setAttribute("checked","checked"),fe.setAttribute("name","t"),ce.appendChild(fe),y.checkClone=ce.cloneNode(!0).cloneNode(!0).lastChild.checked,ce.innerHTML="<textarea>x</textarea>",y.noCloneChecked=!!ce.cloneNode(!0).lastChild.defaultValue,ce.innerHTML="<option></option>",y.option=!!ce.lastChild;var ge={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function ve(e,t){var n;return n="undefined"!=typeof e.getElementsByTagName?e.getElementsByTagName(t||"*"):"undefined"!=typeof e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&A(e,t)?S.merge([e],n):n}function ye(e,t){for(var n=0,r=e.length;n<r;n++)Y.set(e[n],"globalEval",!t||Y.get(t[n],"globalEval"))}ge.tbody=ge.tfoot=ge.colgroup=ge.caption=ge.thead,ge.th=ge.td,y.option||(ge.optgroup=ge.option=[1,"<select multiple='multiple'>","</select>"]);var me=/<|&#?\w+;/;function xe(e,t,n,r,i){for(var o,a,s,u,l,c,f=t.createDocumentFragment(),p=[],d=0,h=e.length;d<h;d++)if((o=e[d])||0===o)if("object"===w(o))S.merge(p,o.nodeType?[o]:o);else if(me.test(o)){a=a||f.appendChild(t.createElement("div")),s=(de.exec(o)||["",""])[1].toLowerCase(),u=ge[s]||ge._default,a.innerHTML=u[1]+S.htmlPrefilter(o)+u[2],c=u[0];while(c--)a=a.lastChild;S.merge(p,a.childNodes),(a=f.firstChild).textContent=""}else p.push(t.createTextNode(o));f.textContent="",d=0;while(o=p[d++])if(r&&-1<S.inArray(o,r))i&&i.push(o);else if(l=ie(o),a=ve(f.appendChild(o),"script"),l&&ye(a),n){c=0;while(o=a[c++])he.test(o.type||"")&&n.push(o)}return f}var be=/^key/,we=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Te=/^([^.]*)(?:\.(.+)|)/;function Ce(){return!0}function Ee(){return!1}function Se(e,t){return e===function(){try{return E.activeElement}catch(e){}}()==("focus"===t)}function ke(e,t,n,r,i,o){var a,s;if("object"==typeof t){for(s in"string"!=typeof n&&(r=r||n,n=void 0),t)ke(e,s,n,r,t[s],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=Ee;else if(!i)return e;return 1===o&&(a=i,(i=function(e){return S().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=S.guid++)),e.each(function(){S.event.add(this,t,i,r,n)})}function Ae(e,i,o){o?(Y.set(e,i,!1),S.event.add(e,i,{namespace:!1,handler:function(e){var t,n,r=Y.get(this,i);if(1&e.isTrigger&&this[i]){if(r.length)(S.event.special[i]||{}).delegateType&&e.stopPropagation();else if(r=s.call(arguments),Y.set(this,i,r),t=o(this,i),this[i](),r!==(n=Y.get(this,i))||t?Y.set(this,i,!1):n={},r!==n)return e.stopImmediatePropagation(),e.preventDefault(),n.value}else r.length&&(Y.set(this,i,{value:S.event.trigger(S.extend(r[0],S.Event.prototype),r.slice(1),this)}),e.stopImmediatePropagation())}})):void 0===Y.get(e,i)&&S.event.add(e,i,Ce)}S.event={global:{},add:function(t,e,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,v=Y.get(t);if(V(t)){n.handler&&(n=(o=n).handler,i=o.selector),i&&S.find.matchesSelector(re,i),n.guid||(n.guid=S.guid++),(u=v.events)||(u=v.events=Object.create(null)),(a=v.handle)||(a=v.handle=function(e){return"undefined"!=typeof S&&S.event.triggered!==e.type?S.event.dispatch.apply(t,arguments):void 0}),l=(e=(e||"").match(P)||[""]).length;while(l--)d=g=(s=Te.exec(e[l])||[])[1],h=(s[2]||"").split(".").sort(),d&&(f=S.event.special[d]||{},d=(i?f.delegateType:f.bindType)||d,f=S.event.special[d]||{},c=S.extend({type:d,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&S.expr.match.needsContext.test(i),namespace:h.join(".")},o),(p=u[d])||((p=u[d]=[]).delegateCount=0,f.setup&&!1!==f.setup.call(t,r,h,a)||t.addEventListener&&t.addEventListener(d,a)),f.add&&(f.add.call(t,c),c.handler.guid||(c.handler.guid=n.guid)),i?p.splice(p.delegateCount++,0,c):p.push(c),S.event.global[d]=!0)}},remove:function(e,t,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,v=Y.hasData(e)&&Y.get(e);if(v&&(u=v.events)){l=(t=(t||"").match(P)||[""]).length;while(l--)if(d=g=(s=Te.exec(t[l])||[])[1],h=(s[2]||"").split(".").sort(),d){f=S.event.special[d]||{},p=u[d=(r?f.delegateType:f.bindType)||d]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=p.length;while(o--)c=p[o],!i&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(p.splice(o,1),c.selector&&p.delegateCount--,f.remove&&f.remove.call(e,c));a&&!p.length&&(f.teardown&&!1!==f.teardown.call(e,h,v.handle)||S.removeEvent(e,d,v.handle),delete u[d])}else for(d in u)S.event.remove(e,d+t[l],n,r,!0);S.isEmptyObject(u)&&Y.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,a,s=new Array(arguments.length),u=S.event.fix(e),l=(Y.get(this,"events")||Object.create(null))[u.type]||[],c=S.event.special[u.type]||{};for(s[0]=u,t=1;t<arguments.length;t++)s[t]=arguments[t];if(u.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,u)){a=S.event.handlers.call(this,u,l),t=0;while((i=a[t++])&&!u.isPropagationStopped()){u.currentTarget=i.elem,n=0;while((o=i.handlers[n++])&&!u.isImmediatePropagationStopped())u.rnamespace&&!1!==o.namespace&&!u.rnamespace.test(o.namespace)||(u.handleObj=o,u.data=o.data,void 0!==(r=((S.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,s))&&!1===(u.result=r)&&(u.preventDefault(),u.stopPropagation()))}return c.postDispatch&&c.postDispatch.call(this,u),u.result}},handlers:function(e,t){var n,r,i,o,a,s=[],u=t.delegateCount,l=e.target;if(u&&l.nodeType&&!("click"===e.type&&1<=e.button))for(;l!==this;l=l.parentNode||this)if(1===l.nodeType&&("click"!==e.type||!0!==l.disabled)){for(o=[],a={},n=0;n<u;n++)void 0===a[i=(r=t[n]).selector+" "]&&(a[i]=r.needsContext?-1<S(i,this).index(l):S.find(i,this,null,[l]).length),a[i]&&o.push(r);o.length&&s.push({elem:l,handlers:o})}return l=this,u<t.length&&s.push({elem:l,handlers:t.slice(u)}),s},addProp:function(t,e){Object.defineProperty(S.Event.prototype,t,{enumerable:!0,configurable:!0,get:m(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[S.expando]?e:new S.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return pe.test(t.type)&&t.click&&A(t,"input")&&Ae(t,"click",Ce),!1},trigger:function(e){var t=this||e;return pe.test(t.type)&&t.click&&A(t,"input")&&Ae(t,"click"),!0},_default:function(e){var t=e.target;return pe.test(t.type)&&t.click&&A(t,"input")&&Y.get(t,"click")||A(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},S.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},S.Event=function(e,t){if(!(this instanceof S.Event))return new S.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?Ce:Ee,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&S.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[S.expando]=!0},S.Event.prototype={constructor:S.Event,isDefaultPrevented:Ee,isPropagationStopped:Ee,isImmediatePropagationStopped:Ee,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Ce,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Ce,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=Ce,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},S.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,"char":!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(e){var t=e.button;return null==e.which&&be.test(e.type)?null!=e.charCode?e.charCode:e.keyCode:!e.which&&void 0!==t&&we.test(e.type)?1&t?1:2&t?3:4&t?2:0:e.which}},S.event.addProp),S.each({focus:"focusin",blur:"focusout"},function(e,t){S.event.special[e]={setup:function(){return Ae(this,e,Se),!1},trigger:function(){return Ae(this,e),!0},delegateType:t}}),S.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,i){S.event.special[e]={delegateType:i,bindType:i,handle:function(e){var t,n=e.relatedTarget,r=e.handleObj;return n&&(n===this||S.contains(this,n))||(e.type=r.origType,t=r.handler.apply(this,arguments),e.type=i),t}}}),S.fn.extend({on:function(e,t,n,r){return ke(this,e,t,n,r)},one:function(e,t,n,r){return ke(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,S(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(i in e)this.off(i,t,e[i]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=Ee),this.each(function(){S.event.remove(this,e,n,t)})}});var Ne=/<script|<style|<link/i,De=/checked\s*(?:[^=]|=\s*.checked.)/i,je=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function qe(e,t){return A(e,"table")&&A(11!==t.nodeType?t:t.firstChild,"tr")&&S(e).children("tbody")[0]||e}function Le(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function He(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function Oe(e,t){var n,r,i,o,a,s;if(1===t.nodeType){if(Y.hasData(e)&&(s=Y.get(e).events))for(i in Y.remove(t,"handle events"),s)for(n=0,r=s[i].length;n<r;n++)S.event.add(t,i,s[i][n]);Q.hasData(e)&&(o=Q.access(e),a=S.extend({},o),Q.set(t,a))}}function Pe(n,r,i,o){r=g(r);var e,t,a,s,u,l,c=0,f=n.length,p=f-1,d=r[0],h=m(d);if(h||1<f&&"string"==typeof d&&!y.checkClone&&De.test(d))return n.each(function(e){var t=n.eq(e);h&&(r[0]=d.call(this,e,t.html())),Pe(t,r,i,o)});if(f&&(t=(e=xe(r,n[0].ownerDocument,!1,n,o)).firstChild,1===e.childNodes.length&&(e=t),t||o)){for(s=(a=S.map(ve(e,"script"),Le)).length;c<f;c++)u=e,c!==p&&(u=S.clone(u,!0,!0),s&&S.merge(a,ve(u,"script"))),i.call(n[c],u,c);if(s)for(l=a[a.length-1].ownerDocument,S.map(a,He),c=0;c<s;c++)u=a[c],he.test(u.type||"")&&!Y.access(u,"globalEval")&&S.contains(l,u)&&(u.src&&"module"!==(u.type||"").toLowerCase()?S._evalUrl&&!u.noModule&&S._evalUrl(u.src,{nonce:u.nonce||u.getAttribute("nonce")},l):b(u.textContent.replace(je,""),u,l))}return n}function Re(e,t,n){for(var r,i=t?S.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||S.cleanData(ve(r)),r.parentNode&&(n&&ie(r)&&ye(ve(r,"script")),r.parentNode.removeChild(r));return e}S.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,a,s,u,l,c=e.cloneNode(!0),f=ie(e);if(!(y.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||S.isXMLDoc(e)))for(a=ve(c),r=0,i=(o=ve(e)).length;r<i;r++)s=o[r],u=a[r],void 0,"input"===(l=u.nodeName.toLowerCase())&&pe.test(s.type)?u.checked=s.checked:"input"!==l&&"textarea"!==l||(u.defaultValue=s.defaultValue);if(t)if(n)for(o=o||ve(e),a=a||ve(c),r=0,i=o.length;r<i;r++)Oe(o[r],a[r]);else Oe(e,c);return 0<(a=ve(c,"script")).length&&ye(a,!f&&ve(e,"script")),c},cleanData:function(e){for(var t,n,r,i=S.event.special,o=0;void 0!==(n=e[o]);o++)if(V(n)){if(t=n[Y.expando]){if(t.events)for(r in t.events)i[r]?S.event.remove(n,r):S.removeEvent(n,r,t.handle);n[Y.expando]=void 0}n[Q.expando]&&(n[Q.expando]=void 0)}}}),S.fn.extend({detach:function(e){return Re(this,e,!0)},remove:function(e){return Re(this,e)},text:function(e){return $(this,function(e){return void 0===e?S.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return Pe(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||qe(this,e).appendChild(e)})},prepend:function(){return Pe(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=qe(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return Pe(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return Pe(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(S.cleanData(ve(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return S.clone(this,e,t)})},html:function(e){return $(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Ne.test(e)&&!ge[(de.exec(e)||["",""])[1].toLowerCase()]){e=S.htmlPrefilter(e);try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(S.cleanData(ve(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return Pe(this,arguments,function(e){var t=this.parentNode;S.inArray(this,n)<0&&(S.cleanData(ve(this)),t&&t.replaceChild(e,this))},n)}}),S.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,a){S.fn[e]=function(e){for(var t,n=[],r=S(e),i=r.length-1,o=0;o<=i;o++)t=o===i?this:this.clone(!0),S(r[o])[a](t),u.apply(n,t.get());return this.pushStack(n)}});var Me=new RegExp("^("+ee+")(?!px)[a-z%]+$","i"),Ie=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=C),t.getComputedStyle(e)},We=function(e,t,n){var r,i,o={};for(i in t)o[i]=e.style[i],e.style[i]=t[i];for(i in r=n.call(e),t)e.style[i]=o[i];return r},Fe=new RegExp(ne.join("|"),"i");function Be(e,t,n){var r,i,o,a,s=e.style;return(n=n||Ie(e))&&(""!==(a=n.getPropertyValue(t)||n[t])||ie(e)||(a=S.style(e,t)),!y.pixelBoxStyles()&&Me.test(a)&&Fe.test(t)&&(r=s.width,i=s.minWidth,o=s.maxWidth,s.minWidth=s.maxWidth=s.width=a,a=n.width,s.width=r,s.minWidth=i,s.maxWidth=o)),void 0!==a?a+"":a}function $e(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function e(){if(l){u.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",l.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",re.appendChild(u).appendChild(l);var e=C.getComputedStyle(l);n="1%"!==e.top,s=12===t(e.marginLeft),l.style.right="60%",o=36===t(e.right),r=36===t(e.width),l.style.position="absolute",i=12===t(l.offsetWidth/3),re.removeChild(u),l=null}}function t(e){return Math.round(parseFloat(e))}var n,r,i,o,a,s,u=E.createElement("div"),l=E.createElement("div");l.style&&(l.style.backgroundClip="content-box",l.cloneNode(!0).style.backgroundClip="",y.clearCloneStyle="content-box"===l.style.backgroundClip,S.extend(y,{boxSizingReliable:function(){return e(),r},pixelBoxStyles:function(){return e(),o},pixelPosition:function(){return e(),n},reliableMarginLeft:function(){return e(),s},scrollboxSize:function(){return e(),i},reliableTrDimensions:function(){var e,t,n,r;return null==a&&(e=E.createElement("table"),t=E.createElement("tr"),n=E.createElement("div"),e.style.cssText="position:absolute;left:-11111px",t.style.height="1px",n.style.height="9px",re.appendChild(e).appendChild(t).appendChild(n),r=C.getComputedStyle(t),a=3<parseInt(r.height),re.removeChild(e)),a}}))}();var _e=["Webkit","Moz","ms"],ze=E.createElement("div").style,Ue={};function Xe(e){var t=S.cssProps[e]||Ue[e];return t||(e in ze?e:Ue[e]=function(e){var t=e[0].toUpperCase()+e.slice(1),n=_e.length;while(n--)if((e=_e[n]+t)in ze)return e}(e)||e)}var Ve=/^(none|table(?!-c[ea]).+)/,Ge=/^--/,Ye={position:"absolute",visibility:"hidden",display:"block"},Qe={letterSpacing:"0",fontWeight:"400"};function Je(e,t,n){var r=te.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function Ke(e,t,n,r,i,o){var a="width"===t?1:0,s=0,u=0;if(n===(r?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(u+=S.css(e,n+ne[a],!0,i)),r?("content"===n&&(u-=S.css(e,"padding"+ne[a],!0,i)),"margin"!==n&&(u-=S.css(e,"border"+ne[a]+"Width",!0,i))):(u+=S.css(e,"padding"+ne[a],!0,i),"padding"!==n?u+=S.css(e,"border"+ne[a]+"Width",!0,i):s+=S.css(e,"border"+ne[a]+"Width",!0,i));return!r&&0<=o&&(u+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-u-s-.5))||0),u}function Ze(e,t,n){var r=Ie(e),i=(!y.boxSizingReliable()||n)&&"border-box"===S.css(e,"boxSizing",!1,r),o=i,a=Be(e,t,r),s="offset"+t[0].toUpperCase()+t.slice(1);if(Me.test(a)){if(!n)return a;a="auto"}return(!y.boxSizingReliable()&&i||!y.reliableTrDimensions()&&A(e,"tr")||"auto"===a||!parseFloat(a)&&"inline"===S.css(e,"display",!1,r))&&e.getClientRects().length&&(i="border-box"===S.css(e,"boxSizing",!1,r),(o=s in e)&&(a=e[s])),(a=parseFloat(a)||0)+Ke(e,t,n||(i?"border":"content"),o,r,a)+"px"}function et(e,t,n,r,i){return new et.prototype.init(e,t,n,r,i)}S.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Be(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=X(t),u=Ge.test(t),l=e.style;if(u||(t=Xe(s)),a=S.cssHooks[t]||S.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:l[t];"string"===(o=typeof n)&&(i=te.exec(n))&&i[1]&&(n=se(e,t,i),o="number"),null!=n&&n==n&&("number"!==o||u||(n+=i&&i[3]||(S.cssNumber[s]?"":"px")),y.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r))||(u?l.setProperty(t,n):l[t]=n))}},css:function(e,t,n,r){var i,o,a,s=X(t);return Ge.test(t)||(t=Xe(s)),(a=S.cssHooks[t]||S.cssHooks[s])&&"get"in a&&(i=a.get(e,!0,n)),void 0===i&&(i=Be(e,t,r)),"normal"===i&&t in Qe&&(i=Qe[t]),""===n||n?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),S.each(["height","width"],function(e,u){S.cssHooks[u]={get:function(e,t,n){if(t)return!Ve.test(S.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?Ze(e,u,n):We(e,Ye,function(){return Ze(e,u,n)})},set:function(e,t,n){var r,i=Ie(e),o=!y.scrollboxSize()&&"absolute"===i.position,a=(o||n)&&"border-box"===S.css(e,"boxSizing",!1,i),s=n?Ke(e,u,n,a,i):0;return a&&o&&(s-=Math.ceil(e["offset"+u[0].toUpperCase()+u.slice(1)]-parseFloat(i[u])-Ke(e,u,"border",!1,i)-.5)),s&&(r=te.exec(t))&&"px"!==(r[3]||"px")&&(e.style[u]=t,t=S.css(e,u)),Je(0,t,s)}}}),S.cssHooks.marginLeft=$e(y.reliableMarginLeft,function(e,t){if(t)return(parseFloat(Be(e,"marginLeft"))||e.getBoundingClientRect().left-We(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),S.each({margin:"",padding:"",border:"Width"},function(i,o){S.cssHooks[i+o]={expand:function(e){for(var t=0,n={},r="string"==typeof e?e.split(" "):[e];t<4;t++)n[i+ne[t]+o]=r[t]||r[t-2]||r[0];return n}},"margin"!==i&&(S.cssHooks[i+o].set=Je)}),S.fn.extend({css:function(e,t){return $(this,function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=Ie(e),i=t.length;a<i;a++)o[t[a]]=S.css(e,t[a],!1,r);return o}return void 0!==n?S.style(e,t,n):S.css(e,t)},e,t,1<arguments.length)}}),((S.Tween=et).prototype={constructor:et,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||S.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(S.cssNumber[n]?"":"px")},cur:function(){var e=et.propHooks[this.prop];return e&&e.get?e.get(this):et.propHooks._default.get(this)},run:function(e){var t,n=et.propHooks[this.prop];return this.options.duration?this.pos=t=S.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):et.propHooks._default.set(this),this}}).init.prototype=et.prototype,(et.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=S.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){S.fx.step[e.prop]?S.fx.step[e.prop](e):1!==e.elem.nodeType||!S.cssHooks[e.prop]&&null==e.elem.style[Xe(e.prop)]?e.elem[e.prop]=e.now:S.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=et.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},S.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},S.fx=et.prototype.init,S.fx.step={};var tt,nt,rt,it,ot=/^(?:toggle|show|hide)$/,at=/queueHooks$/;function st(){nt&&(!1===E.hidden&&C.requestAnimationFrame?C.requestAnimationFrame(st):C.setTimeout(st,S.fx.interval),S.fx.tick())}function ut(){return C.setTimeout(function(){tt=void 0}),tt=Date.now()}function lt(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)i["margin"+(n=ne[r])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function ct(e,t,n){for(var r,i=(ft.tweeners[t]||[]).concat(ft.tweeners["*"]),o=0,a=i.length;o<a;o++)if(r=i[o].call(n,t,e))return r}function ft(o,e,t){var n,a,r=0,i=ft.prefilters.length,s=S.Deferred().always(function(){delete u.elem}),u=function(){if(a)return!1;for(var e=tt||ut(),t=Math.max(0,l.startTime+l.duration-e),n=1-(t/l.duration||0),r=0,i=l.tweens.length;r<i;r++)l.tweens[r].run(n);return s.notifyWith(o,[l,n,t]),n<1&&i?t:(i||s.notifyWith(o,[l,1,0]),s.resolveWith(o,[l]),!1)},l=s.promise({elem:o,props:S.extend({},e),opts:S.extend(!0,{specialEasing:{},easing:S.easing._default},t),originalProperties:e,originalOptions:t,startTime:tt||ut(),duration:t.duration,tweens:[],createTween:function(e,t){var n=S.Tween(o,l.opts,e,t,l.opts.specialEasing[e]||l.opts.easing);return l.tweens.push(n),n},stop:function(e){var t=0,n=e?l.tweens.length:0;if(a)return this;for(a=!0;t<n;t++)l.tweens[t].run(1);return e?(s.notifyWith(o,[l,1,0]),s.resolveWith(o,[l,e])):s.rejectWith(o,[l,e]),this}}),c=l.props;for(!function(e,t){var n,r,i,o,a;for(n in e)if(i=t[r=X(n)],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(a=S.cssHooks[r])&&"expand"in a)for(n in o=a.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}(c,l.opts.specialEasing);r<i;r++)if(n=ft.prefilters[r].call(l,o,c,l.opts))return m(n.stop)&&(S._queueHooks(l.elem,l.opts.queue).stop=n.stop.bind(n)),n;return S.map(c,ct,l),m(l.opts.start)&&l.opts.start.call(o,l),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always),S.fx.timer(S.extend(u,{elem:o,anim:l,queue:l.opts.queue})),l}S.Animation=S.extend(ft,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return se(n.elem,e,te.exec(t),n),n}]},tweener:function(e,t){m(e)?(t=e,e=["*"]):e=e.match(P);for(var n,r=0,i=e.length;r<i;r++)n=e[r],ft.tweeners[n]=ft.tweeners[n]||[],ft.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var r,i,o,a,s,u,l,c,f="width"in t||"height"in t,p=this,d={},h=e.style,g=e.nodeType&&ae(e),v=Y.get(e,"fxshow");for(r in n.queue||(null==(a=S._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,p.always(function(){p.always(function(){a.unqueued--,S.queue(e,"fx").length||a.empty.fire()})})),t)if(i=t[r],ot.test(i)){if(delete t[r],o=o||"toggle"===i,i===(g?"hide":"show")){if("show"!==i||!v||void 0===v[r])continue;g=!0}d[r]=v&&v[r]||S.style(e,r)}if((u=!S.isEmptyObject(t))||!S.isEmptyObject(d))for(r in f&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],null==(l=v&&v.display)&&(l=Y.get(e,"display")),"none"===(c=S.css(e,"display"))&&(l?c=l:(le([e],!0),l=e.style.display||l,c=S.css(e,"display"),le([e]))),("inline"===c||"inline-block"===c&&null!=l)&&"none"===S.css(e,"float")&&(u||(p.done(function(){h.display=l}),null==l&&(c=h.display,l="none"===c?"":c)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",p.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),u=!1,d)u||(v?"hidden"in v&&(g=v.hidden):v=Y.access(e,"fxshow",{display:l}),o&&(v.hidden=!g),g&&le([e],!0),p.done(function(){for(r in g||le([e]),Y.remove(e,"fxshow"),d)S.style(e,r,d[r])})),u=ct(g?v[r]:0,r,p),r in v||(v[r]=u.start,g&&(u.end=u.start,u.start=0))}],prefilter:function(e,t){t?ft.prefilters.unshift(e):ft.prefilters.push(e)}}),S.speed=function(e,t,n){var r=e&&"object"==typeof e?S.extend({},e):{complete:n||!n&&t||m(e)&&e,duration:e,easing:n&&t||t&&!m(t)&&t};return S.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in S.fx.speeds?r.duration=S.fx.speeds[r.duration]:r.duration=S.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){m(r.old)&&r.old.call(this),r.queue&&S.dequeue(this,r.queue)},r},S.fn.extend({fadeTo:function(e,t,n,r){return this.filter(ae).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(t,e,n,r){var i=S.isEmptyObject(t),o=S.speed(e,n,r),a=function(){var e=ft(this,S.extend({},t),o);(i||Y.get(this,"finish"))&&e.stop(!0)};return a.finish=a,i||!1===o.queue?this.each(a):this.queue(o.queue,a)},stop:function(i,e,o){var a=function(e){var t=e.stop;delete e.stop,t(o)};return"string"!=typeof i&&(o=e,e=i,i=void 0),e&&this.queue(i||"fx",[]),this.each(function(){var e=!0,t=null!=i&&i+"queueHooks",n=S.timers,r=Y.get(this);if(t)r[t]&&r[t].stop&&a(r[t]);else for(t in r)r[t]&&r[t].stop&&at.test(t)&&a(r[t]);for(t=n.length;t--;)n[t].elem!==this||null!=i&&n[t].queue!==i||(n[t].anim.stop(o),e=!1,n.splice(t,1));!e&&o||S.dequeue(this,i)})},finish:function(a){return!1!==a&&(a=a||"fx"),this.each(function(){var e,t=Y.get(this),n=t[a+"queue"],r=t[a+"queueHooks"],i=S.timers,o=n?n.length:0;for(t.finish=!0,S.queue(this,a,[]),r&&r.stop&&r.stop.call(this,!0),e=i.length;e--;)i[e].elem===this&&i[e].queue===a&&(i[e].anim.stop(!0),i.splice(e,1));for(e=0;e<o;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),S.each(["toggle","show","hide"],function(e,r){var i=S.fn[r];S.fn[r]=function(e,t,n){return null==e||"boolean"==typeof e?i.apply(this,arguments):this.animate(lt(r,!0),e,t,n)}}),S.each({slideDown:lt("show"),slideUp:lt("hide"),slideToggle:lt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,r){S.fn[e]=function(e,t,n){return this.animate(r,e,t,n)}}),S.timers=[],S.fx.tick=function(){var e,t=0,n=S.timers;for(tt=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||S.fx.stop(),tt=void 0},S.fx.timer=function(e){S.timers.push(e),S.fx.start()},S.fx.interval=13,S.fx.start=function(){nt||(nt=!0,st())},S.fx.stop=function(){nt=null},S.fx.speeds={slow:600,fast:200,_default:400},S.fn.delay=function(r,e){return r=S.fx&&S.fx.speeds[r]||r,e=e||"fx",this.queue(e,function(e,t){var n=C.setTimeout(e,r);t.stop=function(){C.clearTimeout(n)}})},rt=E.createElement("input"),it=E.createElement("select").appendChild(E.createElement("option")),rt.type="checkbox",y.checkOn=""!==rt.value,y.optSelected=it.selected,(rt=E.createElement("input")).value="t",rt.type="radio",y.radioValue="t"===rt.value;var pt,dt=S.expr.attrHandle;S.fn.extend({attr:function(e,t){return $(this,S.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){S.removeAttr(this,e)})}}),S.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return"undefined"==typeof e.getAttribute?S.prop(e,t,n):(1===o&&S.isXMLDoc(e)||(i=S.attrHooks[t.toLowerCase()]||(S.expr.match.bool.test(t)?pt:void 0)),void 0!==n?null===n?void S.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):i&&"get"in i&&null!==(r=i.get(e,t))?r:null==(r=S.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){if(!y.radioValue&&"radio"===t&&A(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(P);if(i&&1===e.nodeType)while(n=i[r++])e.removeAttribute(n)}}),pt={set:function(e,t,n){return!1===t?S.removeAttr(e,n):e.setAttribute(n,n),n}},S.each(S.expr.match.bool.source.match(/\w+/g),function(e,t){var a=dt[t]||S.find.attr;dt[t]=function(e,t,n){var r,i,o=t.toLowerCase();return n||(i=dt[o],dt[o]=r,r=null!=a(e,t,n)?o:null,dt[o]=i),r}});var ht=/^(?:input|select|textarea|button)$/i,gt=/^(?:a|area)$/i;function vt(e){return(e.match(P)||[]).join(" ")}function yt(e){return e.getAttribute&&e.getAttribute("class")||""}function mt(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(P)||[]}S.fn.extend({prop:function(e,t){return $(this,S.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[S.propFix[e]||e]})}}),S.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&S.isXMLDoc(e)||(t=S.propFix[t]||t,i=S.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=S.find.attr(e,"tabindex");return t?parseInt(t,10):ht.test(e.nodeName)||gt.test(e.nodeName)&&e.href?0:-1}}},propFix:{"for":"htmlFor","class":"className"}}),y.optSelected||(S.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),S.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){S.propFix[this.toLowerCase()]=this}),S.fn.extend({addClass:function(t){var e,n,r,i,o,a,s,u=0;if(m(t))return this.each(function(e){S(this).addClass(t.call(this,e,yt(this)))});if((e=mt(t)).length)while(n=this[u++])if(i=yt(n),r=1===n.nodeType&&" "+vt(i)+" "){a=0;while(o=e[a++])r.indexOf(" "+o+" ")<0&&(r+=o+" ");i!==(s=vt(r))&&n.setAttribute("class",s)}return this},removeClass:function(t){var e,n,r,i,o,a,s,u=0;if(m(t))return this.each(function(e){S(this).removeClass(t.call(this,e,yt(this)))});if(!arguments.length)return this.attr("class","");if((e=mt(t)).length)while(n=this[u++])if(i=yt(n),r=1===n.nodeType&&" "+vt(i)+" "){a=0;while(o=e[a++])while(-1<r.indexOf(" "+o+" "))r=r.replace(" "+o+" "," ");i!==(s=vt(r))&&n.setAttribute("class",s)}return this},toggleClass:function(i,t){var o=typeof i,a="string"===o||Array.isArray(i);return"boolean"==typeof t&&a?t?this.addClass(i):this.removeClass(i):m(i)?this.each(function(e){S(this).toggleClass(i.call(this,e,yt(this),t),t)}):this.each(function(){var e,t,n,r;if(a){t=0,n=S(this),r=mt(i);while(e=r[t++])n.hasClass(e)?n.removeClass(e):n.addClass(e)}else void 0!==i&&"boolean"!==o||((e=yt(this))&&Y.set(this,"__className__",e),this.setAttribute&&this.setAttribute("class",e||!1===i?"":Y.get(this,"__className__")||""))})},hasClass:function(e){var t,n,r=0;t=" "+e+" ";while(n=this[r++])if(1===n.nodeType&&-1<(" "+vt(yt(n))+" ").indexOf(t))return!0;return!1}});var xt=/\r/g;S.fn.extend({val:function(n){var r,e,i,t=this[0];return arguments.length?(i=m(n),this.each(function(e){var t;1===this.nodeType&&(null==(t=i?n.call(this,e,S(this).val()):n)?t="":"number"==typeof t?t+="":Array.isArray(t)&&(t=S.map(t,function(e){return null==e?"":e+""})),(r=S.valHooks[this.type]||S.valHooks[this.nodeName.toLowerCase()])&&"set"in r&&void 0!==r.set(this,t,"value")||(this.value=t))})):t?(r=S.valHooks[t.type]||S.valHooks[t.nodeName.toLowerCase()])&&"get"in r&&void 0!==(e=r.get(t,"value"))?e:"string"==typeof(e=t.value)?e.replace(xt,""):null==e?"":e:void 0}}),S.extend({valHooks:{option:{get:function(e){var t=S.find.attr(e,"value");return null!=t?t:vt(S.text(e))}},select:{get:function(e){var t,n,r,i=e.options,o=e.selectedIndex,a="select-one"===e.type,s=a?null:[],u=a?o+1:i.length;for(r=o<0?u:a?o:0;r<u;r++)if(((n=i[r]).selected||r===o)&&!n.disabled&&(!n.parentNode.disabled||!A(n.parentNode,"optgroup"))){if(t=S(n).val(),a)return t;s.push(t)}return s},set:function(e,t){var n,r,i=e.options,o=S.makeArray(t),a=i.length;while(a--)((r=i[a]).selected=-1<S.inArray(S.valHooks.option.get(r),o))&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),S.each(["radio","checkbox"],function(){S.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<S.inArray(S(e).val(),t)}},y.checkOn||(S.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),y.focusin="onfocusin"in C;var bt=/^(?:focusinfocus|focusoutblur)$/,wt=function(e){e.stopPropagation()};S.extend(S.event,{trigger:function(e,t,n,r){var i,o,a,s,u,l,c,f,p=[n||E],d=v.call(e,"type")?e.type:e,h=v.call(e,"namespace")?e.namespace.split("."):[];if(o=f=a=n=n||E,3!==n.nodeType&&8!==n.nodeType&&!bt.test(d+S.event.triggered)&&(-1<d.indexOf(".")&&(d=(h=d.split(".")).shift(),h.sort()),u=d.indexOf(":")<0&&"on"+d,(e=e[S.expando]?e:new S.Event(d,"object"==typeof e&&e)).isTrigger=r?2:3,e.namespace=h.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:S.makeArray(t,[e]),c=S.event.special[d]||{},r||!c.trigger||!1!==c.trigger.apply(n,t))){if(!r&&!c.noBubble&&!x(n)){for(s=c.delegateType||d,bt.test(s+d)||(o=o.parentNode);o;o=o.parentNode)p.push(o),a=o;a===(n.ownerDocument||E)&&p.push(a.defaultView||a.parentWindow||C)}i=0;while((o=p[i++])&&!e.isPropagationStopped())f=o,e.type=1<i?s:c.bindType||d,(l=(Y.get(o,"events")||Object.create(null))[e.type]&&Y.get(o,"handle"))&&l.apply(o,t),(l=u&&o[u])&&l.apply&&V(o)&&(e.result=l.apply(o,t),!1===e.result&&e.preventDefault());return e.type=d,r||e.isDefaultPrevented()||c._default&&!1!==c._default.apply(p.pop(),t)||!V(n)||u&&m(n[d])&&!x(n)&&((a=n[u])&&(n[u]=null),S.event.triggered=d,e.isPropagationStopped()&&f.addEventListener(d,wt),n[d](),e.isPropagationStopped()&&f.removeEventListener(d,wt),S.event.triggered=void 0,a&&(n[u]=a)),e.result}},simulate:function(e,t,n){var r=S.extend(new S.Event,n,{type:e,isSimulated:!0});S.event.trigger(r,null,t)}}),S.fn.extend({trigger:function(e,t){return this.each(function(){S.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return S.event.trigger(e,t,n,!0)}}),y.focusin||S.each({focus:"focusin",blur:"focusout"},function(n,r){var i=function(e){S.event.simulate(r,e.target,S.event.fix(e))};S.event.special[r]={setup:function(){var e=this.ownerDocument||this.document||this,t=Y.access(e,r);t||e.addEventListener(n,i,!0),Y.access(e,r,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=Y.access(e,r)-1;t?Y.access(e,r,t):(e.removeEventListener(n,i,!0),Y.remove(e,r))}}});var Tt=C.location,Ct={guid:Date.now()},Et=/\?/;S.parseXML=function(e){var t;if(!e||"string"!=typeof e)return null;try{t=(new C.DOMParser).parseFromString(e,"text/xml")}catch(e){t=void 0}return t&&!t.getElementsByTagName("parsererror").length||S.error("Invalid XML: "+e),t};var St=/\[\]$/,kt=/\r?\n/g,At=/^(?:submit|button|image|reset|file)$/i,Nt=/^(?:input|select|textarea|keygen)/i;function Dt(n,e,r,i){var t;if(Array.isArray(e))S.each(e,function(e,t){r||St.test(n)?i(n,t):Dt(n+"["+("object"==typeof t&&null!=t?e:"")+"]",t,r,i)});else if(r||"object"!==w(e))i(n,e);else for(t in e)Dt(n+"["+t+"]",e[t],r,i)}S.param=function(e,t){var n,r=[],i=function(e,t){var n=m(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!S.isPlainObject(e))S.each(e,function(){i(this.name,this.value)});else for(n in e)Dt(n,e[n],t,i);return r.join("&")},S.fn.extend({serialize:function(){return S.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=S.prop(this,"elements");return e?S.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!S(this).is(":disabled")&&Nt.test(this.nodeName)&&!At.test(e)&&(this.checked||!pe.test(e))}).map(function(e,t){var n=S(this).val();return null==n?null:Array.isArray(n)?S.map(n,function(e){return{name:t.name,value:e.replace(kt,"\r\n")}}):{name:t.name,value:n.replace(kt,"\r\n")}}).get()}});var jt=/%20/g,qt=/#.*$/,Lt=/([?&])_=[^&]*/,Ht=/^(.*?):[ \t]*([^\r\n]*)$/gm,Ot=/^(?:GET|HEAD)$/,Pt=/^\/\//,Rt={},Mt={},It="*/".concat("*"),Wt=E.createElement("a");function Ft(o){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,r=0,i=e.toLowerCase().match(P)||[];if(m(t))while(n=i[r++])"+"===n[0]?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(t)):(o[n]=o[n]||[]).push(t)}}function Bt(t,i,o,a){var s={},u=t===Mt;function l(e){var r;return s[e]=!0,S.each(t[e]||[],function(e,t){var n=t(i,o,a);return"string"!=typeof n||u||s[n]?u?!(r=n):void 0:(i.dataTypes.unshift(n),l(n),!1)}),r}return l(i.dataTypes[0])||!s["*"]&&l("*")}function $t(e,t){var n,r,i=S.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r||(r={}))[n]=t[n]);return r&&S.extend(!0,e,r),e}Wt.href=Tt.href,S.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Tt.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Tt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":It,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":S.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?$t($t(e,S.ajaxSettings),t):$t(S.ajaxSettings,e)},ajaxPrefilter:Ft(Rt),ajaxTransport:Ft(Mt),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var c,f,p,n,d,r,h,g,i,o,v=S.ajaxSetup({},t),y=v.context||v,m=v.context&&(y.nodeType||y.jquery)?S(y):S.event,x=S.Deferred(),b=S.Callbacks("once memory"),w=v.statusCode||{},a={},s={},u="canceled",T={readyState:0,getResponseHeader:function(e){var t;if(h){if(!n){n={};while(t=Ht.exec(p))n[t[1].toLowerCase()+" "]=(n[t[1].toLowerCase()+" "]||[]).concat(t[2])}t=n[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return h?p:null},setRequestHeader:function(e,t){return null==h&&(e=s[e.toLowerCase()]=s[e.toLowerCase()]||e,a[e]=t),this},overrideMimeType:function(e){return null==h&&(v.mimeType=e),this},statusCode:function(e){var t;if(e)if(h)T.always(e[T.status]);else for(t in e)w[t]=[w[t],e[t]];return this},abort:function(e){var t=e||u;return c&&c.abort(t),l(0,t),this}};if(x.promise(T),v.url=((e||v.url||Tt.href)+"").replace(Pt,Tt.protocol+"//"),v.type=t.method||t.type||v.method||v.type,v.dataTypes=(v.dataType||"*").toLowerCase().match(P)||[""],null==v.crossDomain){r=E.createElement("a");try{r.href=v.url,r.href=r.href,v.crossDomain=Wt.protocol+"//"+Wt.host!=r.protocol+"//"+r.host}catch(e){v.crossDomain=!0}}if(v.data&&v.processData&&"string"!=typeof v.data&&(v.data=S.param(v.data,v.traditional)),Bt(Rt,v,t,T),h)return T;for(i in(g=S.event&&v.global)&&0==S.active++&&S.event.trigger("ajaxStart"),v.type=v.type.toUpperCase(),v.hasContent=!Ot.test(v.type),f=v.url.replace(qt,""),v.hasContent?v.data&&v.processData&&0===(v.contentType||"").indexOf("application/x-www-form-urlencoded")&&(v.data=v.data.replace(jt,"+")):(o=v.url.slice(f.length),v.data&&(v.processData||"string"==typeof v.data)&&(f+=(Et.test(f)?"&":"?")+v.data,delete v.data),!1===v.cache&&(f=f.replace(Lt,"$1"),o=(Et.test(f)?"&":"?")+"_="+Ct.guid+++o),v.url=f+o),v.ifModified&&(S.lastModified[f]&&T.setRequestHeader("If-Modified-Since",S.lastModified[f]),S.etag[f]&&T.setRequestHeader("If-None-Match",S.etag[f])),(v.data&&v.hasContent&&!1!==v.contentType||t.contentType)&&T.setRequestHeader("Content-Type",v.contentType),T.setRequestHeader("Accept",v.dataTypes[0]&&v.accepts[v.dataTypes[0]]?v.accepts[v.dataTypes[0]]+("*"!==v.dataTypes[0]?", "+It+"; q=0.01":""):v.accepts["*"]),v.headers)T.setRequestHeader(i,v.headers[i]);if(v.beforeSend&&(!1===v.beforeSend.call(y,T,v)||h))return T.abort();if(u="abort",b.add(v.complete),T.done(v.success),T.fail(v.error),c=Bt(Mt,v,t,T)){if(T.readyState=1,g&&m.trigger("ajaxSend",[T,v]),h)return T;v.async&&0<v.timeout&&(d=C.setTimeout(function(){T.abort("timeout")},v.timeout));try{h=!1,c.send(a,l)}catch(e){if(h)throw e;l(-1,e)}}else l(-1,"No Transport");function l(e,t,n,r){var i,o,a,s,u,l=t;h||(h=!0,d&&C.clearTimeout(d),c=void 0,p=r||"",T.readyState=0<e?4:0,i=200<=e&&e<300||304===e,n&&(s=function(e,t,n){var r,i,o,a,s=e.contents,u=e.dataTypes;while("*"===u[0])u.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in s)if(s[i]&&s[i].test(r)){u.unshift(i);break}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||e.converters[i+" "+u[0]]){o=i;break}a||(a=i)}o=o||a}if(o)return o!==u[0]&&u.unshift(o),n[o]}(v,T,n)),!i&&-1<S.inArray("script",v.dataTypes)&&(v.converters["text script"]=function(){}),s=function(e,t,n,r){var i,o,a,s,u,l={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)l[a.toLowerCase()]=e.converters[a];o=c.shift();while(o)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(!(a=l[u+" "+o]||l["* "+o]))for(i in l)if((s=i.split(" "))[1]===o&&(a=l[u+" "+s[0]]||l["* "+s[0]])){!0===a?a=l[i]:!0!==l[i]&&(o=s[0],c.unshift(s[1]));break}if(!0!==a)if(a&&e["throws"])t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+u+" to "+o}}}return{state:"success",data:t}}(v,s,T,i),i?(v.ifModified&&((u=T.getResponseHeader("Last-Modified"))&&(S.lastModified[f]=u),(u=T.getResponseHeader("etag"))&&(S.etag[f]=u)),204===e||"HEAD"===v.type?l="nocontent":304===e?l="notmodified":(l=s.state,o=s.data,i=!(a=s.error))):(a=l,!e&&l||(l="error",e<0&&(e=0))),T.status=e,T.statusText=(t||l)+"",i?x.resolveWith(y,[o,l,T]):x.rejectWith(y,[T,l,a]),T.statusCode(w),w=void 0,g&&m.trigger(i?"ajaxSuccess":"ajaxError",[T,v,i?o:a]),b.fireWith(y,[T,l]),g&&(m.trigger("ajaxComplete",[T,v]),--S.active||S.event.trigger("ajaxStop")))}return T},getJSON:function(e,t,n){return S.get(e,t,n,"json")},getScript:function(e,t){return S.get(e,void 0,t,"script")}}),S.each(["get","post"],function(e,i){S[i]=function(e,t,n,r){return m(t)&&(r=r||n,n=t,t=void 0),S.ajax(S.extend({url:e,type:i,dataType:r,data:t,success:n},S.isPlainObject(e)&&e))}}),S.ajaxPrefilter(function(e){var t;for(t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),S._evalUrl=function(e,t,n){return S.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){S.globalEval(e,t,n)}})},S.fn.extend({wrapAll:function(e){var t;return this[0]&&(m(e)&&(e=e.call(this[0])),t=S(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){var e=this;while(e.firstElementChild)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(n){return m(n)?this.each(function(e){S(this).wrapInner(n.call(this,e))}):this.each(function(){var e=S(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=m(t);return this.each(function(e){S(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){return this.parent(e).not("body").each(function(){S(this).replaceWith(this.childNodes)}),this}}),S.expr.pseudos.hidden=function(e){return!S.expr.pseudos.visible(e)},S.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},S.ajaxSettings.xhr=function(){try{return new C.XMLHttpRequest}catch(e){}};var _t={0:200,1223:204},zt=S.ajaxSettings.xhr();y.cors=!!zt&&"withCredentials"in zt,y.ajax=zt=!!zt,S.ajaxTransport(function(i){var o,a;if(y.cors||zt&&!i.crossDomain)return{send:function(e,t){var n,r=i.xhr();if(r.open(i.type,i.url,i.async,i.username,i.password),i.xhrFields)for(n in i.xhrFields)r[n]=i.xhrFields[n];for(n in i.mimeType&&r.overrideMimeType&&r.overrideMimeType(i.mimeType),i.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)r.setRequestHeader(n,e[n]);o=function(e){return function(){o&&(o=a=r.onload=r.onerror=r.onabort=r.ontimeout=r.onreadystatechange=null,"abort"===e?r.abort():"error"===e?"number"!=typeof r.status?t(0,"error"):t(r.status,r.statusText):t(_t[r.status]||r.status,r.statusText,"text"!==(r.responseType||"text")||"string"!=typeof r.responseText?{binary:r.response}:{text:r.responseText},r.getAllResponseHeaders()))}},r.onload=o(),a=r.onerror=r.ontimeout=o("error"),void 0!==r.onabort?r.onabort=a:r.onreadystatechange=function(){4===r.readyState&&C.setTimeout(function(){o&&a()})},o=o("abort");try{r.send(i.hasContent&&i.data||null)}catch(e){if(o)throw e}},abort:function(){o&&o()}}}),S.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),S.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return S.globalEval(e),e}}}),S.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),S.ajaxTransport("script",function(n){var r,i;if(n.crossDomain||n.scriptAttrs)return{send:function(e,t){r=S("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",i=function(e){r.remove(),i=null,e&&t("error"===e.type?404:200,e.type)}),E.head.appendChild(r[0])},abort:function(){i&&i()}}});var Ut,Xt=[],Vt=/(=)\?(?=&|$)|\?\?/;S.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Xt.pop()||S.expando+"_"+Ct.guid++;return this[e]=!0,e}}),S.ajaxPrefilter("json jsonp",function(e,t,n){var r,i,o,a=!1!==e.jsonp&&(Vt.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Vt.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return r=e.jsonpCallback=m(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(Vt,"$1"+r):!1!==e.jsonp&&(e.url+=(Et.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return o||S.error(r+" was not called"),o[0]},e.dataTypes[0]="json",i=C[r],C[r]=function(){o=arguments},n.always(function(){void 0===i?S(C).removeProp(r):C[r]=i,e[r]&&(e.jsonpCallback=t.jsonpCallback,Xt.push(r)),o&&m(i)&&i(o[0]),o=i=void 0}),"script"}),y.createHTMLDocument=((Ut=E.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===Ut.childNodes.length),S.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(y.createHTMLDocument?((r=(t=E.implementation.createHTMLDocument("")).createElement("base")).href=E.location.href,t.head.appendChild(r)):t=E),o=!n&&[],(i=N.exec(e))?[t.createElement(i[1])]:(i=xe([e],t,o),o&&o.length&&S(o).remove(),S.merge([],i.childNodes)));var r,i,o},S.fn.load=function(e,t,n){var r,i,o,a=this,s=e.indexOf(" ");return-1<s&&(r=vt(e.slice(s)),e=e.slice(0,s)),m(t)?(n=t,t=void 0):t&&"object"==typeof t&&(i="POST"),0<a.length&&S.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done(function(e){o=arguments,a.html(r?S("<div>").append(S.parseHTML(e)).find(r):e)}).always(n&&function(e,t){a.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},S.expr.pseudos.animated=function(t){return S.grep(S.timers,function(e){return t===e.elem}).length},S.offset={setOffset:function(e,t,n){var r,i,o,a,s,u,l=S.css(e,"position"),c=S(e),f={};"static"===l&&(e.style.position="relative"),s=c.offset(),o=S.css(e,"top"),u=S.css(e,"left"),("absolute"===l||"fixed"===l)&&-1<(o+u).indexOf("auto")?(a=(r=c.position()).top,i=r.left):(a=parseFloat(o)||0,i=parseFloat(u)||0),m(t)&&(t=t.call(e,n,S.extend({},s))),null!=t.top&&(f.top=t.top-s.top+a),null!=t.left&&(f.left=t.left-s.left+i),"using"in t?t.using.call(e,f):("number"==typeof f.top&&(f.top+="px"),"number"==typeof f.left&&(f.left+="px"),c.css(f))}},S.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){S.offset.setOffset(this,t,e)});var e,n,r=this[0];return r?r.getClientRects().length?(e=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===S.css(r,"position"))t=r.getBoundingClientRect();else{t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;while(e&&(e===n.body||e===n.documentElement)&&"static"===S.css(e,"position"))e=e.parentNode;e&&e!==r&&1===e.nodeType&&((i=S(e).offset()).top+=S.css(e,"borderTopWidth",!0),i.left+=S.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-S.css(r,"marginTop",!0),left:t.left-i.left-S.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){var e=this.offsetParent;while(e&&"static"===S.css(e,"position"))e=e.offsetParent;return e||re})}}),S.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,i){var o="pageYOffset"===i;S.fn[t]=function(e){return $(this,function(e,t,n){var r;if(x(e)?r=e:9===e.nodeType&&(r=e.defaultView),void 0===n)return r?r[i]:e[t];r?r.scrollTo(o?r.pageXOffset:n,o?n:r.pageYOffset):e[t]=n},t,e,arguments.length)}}),S.each(["top","left"],function(e,n){S.cssHooks[n]=$e(y.pixelPosition,function(e,t){if(t)return t=Be(e,n),Me.test(t)?S(e).position()[n]+"px":t})}),S.each({Height:"height",Width:"width"},function(a,s){S.each({padding:"inner"+a,content:s,"":"outer"+a},function(r,o){S.fn[o]=function(e,t){var n=arguments.length&&(r||"boolean"!=typeof e),i=r||(!0===e||!0===t?"margin":"border");return $(this,function(e,t,n){var r;return x(e)?0===o.indexOf("outer")?e["inner"+a]:e.document.documentElement["client"+a]:9===e.nodeType?(r=e.documentElement,Math.max(e.body["scroll"+a],r["scroll"+a],e.body["offset"+a],r["offset"+a],r["client"+a])):void 0===n?S.css(e,t,i):S.style(e,t,n,i)},s,n?e:void 0,n)}})}),S.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){S.fn[t]=function(e){return this.on(t,e)}}),S.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),S.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){S.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}});var Gt=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;S.proxy=function(e,t){var n,r,i;if("string"==typeof t&&(n=e[t],t=e,e=n),m(e))return r=s.call(arguments,2),(i=function(){return e.apply(t||this,r.concat(s.call(arguments)))}).guid=e.guid=e.guid||S.guid++,i},S.holdReady=function(e){e?S.readyWait++:S.ready(!0)},S.isArray=Array.isArray,S.parseJSON=JSON.parse,S.nodeName=A,S.isFunction=m,S.isWindow=x,S.camelCase=X,S.type=w,S.now=Date.now,S.isNumeric=function(e){var t=S.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},S.trim=function(e){return null==e?"":(e+"").replace(Gt,"")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return S});var Yt=C.jQuery,Qt=C.$;return S.noConflict=function(e){return C.$===S&&(C.$=Qt),e&&C.jQuery===S&&(C.jQuery=Yt),S},"undefined"==typeof e&&(C.jQuery=C.$=S),S});

/* File: assets/js/custom/main.js */
/**
 * Flash - Custom Scripts For This Site
 * ---------------------------------------
 */
flash.ready(function(){    
    // var cookiesblocker = new cookiesIframeBlocker();

    // Fix the position of the body when lightboxes are open
    document.body.addEventListener('lightboxVisible', function(){
        var scroll_top = window.pageYOffset;
        document.body.style.position = 'fixed';
        document.body.style.top = scroll_top * -1 + 'px';
        // cookiesblocker.refresh();

        if(document.querySelector('.lightbox iframe[data-src]')) {
            document.querySelector('.lightbox iframe[data-src]').src = document.querySelector('.lightbox iframe[data-src]').dataset.src;
        }
    });
    
    document.body.addEventListener('lightboxClosed', function(){
        var scroll_top = parseInt(document.body.style.top) * -1;
        document.body.style.position = 'relative';
        document.body.style.top = 0;
        console.log(scroll_top)
        window.scrollBy(0, scroll_top);
    });

    setTimeout(function() { window.followHeightInstance.update(); }, 1000);

    // Calculate time remaining
    if (typeof remaining !== 'undefined') {

        function renderCountdown(dateStart, dateEnd, callback){
            var currentDate = dateStart.getTime();
            var targetDate = dateEnd.getTime(); // set the countdown date
            var days, hours, minutes, seconds; // variables for time units
            var count = 0;
            var getCountdown = function (c) {
                // find the amount of "seconds" between now and target
                var secondsLeft = ((targetDate - currentDate) / 1000) - c;
                days = Math.floor( secondsLeft / 86400 )
                secondsLeft %= 86400;
                hours = Math.floor( secondsLeft / 3600 )
                secondsLeft %= 3600;
                minutes = Math.floor( secondsLeft / 60 )
                seconds = Math.floor( secondsLeft % 60 )
                // format countdown string + set tag value
                var data = {
                    days: days,
                    hours: hours,
                    minutes: minutes,
                    seconds: seconds
                }
                callback(data)
            }
            getCountdown(count);
            setInterval(function () { getCountdown(count++ ); }, 1000);
        }
        
        var a = remaining.split(/[^0-9]/)
        var end = new Date(a[0], a[1] - 1, a[2], a[3], a[4])
        var current = new Date()

        renderCountdown(current, end, function(data) {
            var days_text = (data.days <= 1) ? ' day' : ' days'
            var hours_text = (data.hours <= 1) ? ' hour' : ' hours'
            var minutes_text = (data.minutes <= 1) ? ' minute' : ' minutes'
            
            var formattedRemaining = ''
            if(data.days) formattedRemaining = data.days + days_text
            else if(data.hours) {
                if (data.minutes) {
                    formattedRemaining = data.hours + hours_text + ' and ' + data.minutes + minutes_text
                } else {
                    formattedRemaining = data.hours + hours_text
                }
            } else if(data.minutes) formattedRemaining = data.minutes + minutes_text

            document.querySelectorAll('[data-remaining]').forEach(function(el) {
                el.innerText = formattedRemaining
            })

        })
    }

    window.getDeviceType = function() {
        var ua = navigator.userAgent;
        if (/(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(ua)) {
          return "Tablet";
        }
        if (
          /Mobile|iP(hone|od)|Android|BlackBerry|IEMobile|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(
            ua
          )
        ) {
          return "Mobile";
        }
        return "Desktop";
    }
      
    
});

/* File: assets/js/custom/recaptcha.js */
function recaptchaInit() {
	var $recaptchas = document.querySelectorAll('[data-recaptcha]');
	$recaptchas.forEach(function($el) {
		$el.innerHTML = '';
		var $recaptcha = $el.cloneNode(true);
		$el.insertAdjacentElement('afterend', $recaptcha);
		$el.parentNode.removeChild($el);
		var $form = $recaptcha.closest('form');
		var $submit = $form.querySelector('[type="submit"]');
		if($submit) $submit.disabled = true;
		grecaptcha.render($recaptcha, {
			sitekey: recaptcha_key,
			callback: function() {
				if($submit) $submit.disabled = false;
			}
		});
	});
}

function recaptchaCallback() { recaptchaInit(); }

/* File: assets/js/flash/1.5.2/start.js */

if(typeof flash_first_page === 'undefined') {
	var flash_first_page = true;
}

function flashAssetsLoad() {
    try {
        var ts = document.querySelector('meta[name="ts"]').getAttribute('content');
    } catch(e) {
        var ts = Date.now();
    }
    var rootPath = '/';
    try {
        rootPath = document.querySelector('meta[name="rp"]').getAttribute('content');
    } catch(e) {}
    loadCSS(rootPath + 'assets/css/bundle.min.css?v=' + ts, document.getElementById('loadcssscript') );
    loadCSS(rootPath + 'assets/css/fonts.css', document.getElementById('loadcssscript') );
    
    var wf = document.createElement('script');
    wf.src = rootPath + 'assets/js/webfont.js';
    wf.type = 'text/javascript';
    wf.async = 'true';
    document.head.insertBefore(wf, document.head.firstChild);
}

if(!flash.initialised) {
	var flashReadyEvent = new CustomEvent('flashReady');
	flash.initialised = true;
	flash.start();
    flash_first_page = false;
    flashAssetsLoad();
}