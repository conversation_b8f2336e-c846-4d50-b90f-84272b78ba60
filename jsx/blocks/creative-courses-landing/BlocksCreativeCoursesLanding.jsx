import { useMemo, useState } from "preact/hooks";
import { CoursesTabs } from "./CoursesTabs";
import { useArloVariants } from "../../arlo/useArloVariants";
import { formatDateWithTime } from "../../arlo/dateUtils";

const subjectToCategory = {
  business: "Business Courses",
  "data-analytics": "Data analytics Courses",
  design: "Design Courses",
  "digital-and-it": "Digital & IT Courses",
  finance: "Finance Courses",
  hr: "HR Courses",
  leadership: "Leadership Courses",
  management: "Management Courses",
  marketing: "Marketing Courses",
};

export function BlocksCreativeCoursesLanding({ featured_courses, children, theme, no_tabs, "data-block-id": blockId }) {
  const urlParams = new URLSearchParams(window.location.search);

  const [selectedCourse, setSelectedCourse] = useState(null);

  // Parse featured_courses safely - try script tag first, then fallback to prop
  let featuredCourses = [];

  // Try to get data from script tag first (new method)
  if (blockId) {
    try {
      const scriptElement = document.getElementById(`${blockId}-data`);
      if (scriptElement) {
        const scriptContent = scriptElement.textContent || scriptElement.innerHTML;
        if (scriptContent.trim()) {
          const parsed = JSON.parse(scriptContent);
          featuredCourses = Array.isArray(parsed) ? parsed : [];
        }
      }
    } catch (e) {
      console.error("Failed to parse featured data from script tag:", {
        blockId,
        error: e.message,
      });
    }
  }

  // Fallback to prop method (legacy support)
  if (featuredCourses.length === 0) {
    try {
      if (typeof featured_courses === "string" && featured_courses.trim()) {
        featuredCourses = JSON.parse(featured_courses);
      }
      if (!Array.isArray(featuredCourses)) {
        featuredCourses = [];
      }
    } catch (e) {
      console.error("Failed to parse 'featured_courses' prop:", {
        value: featured_courses,
        error: e.message,
      });
      featuredCourses = [];
    }
  }

  const keywords = urlParams.get("keywords") || "";

  const subject = selectedCourse;
  const results = useMemo(() => {
    // @ts-expect-error
    let allCourses = [...courses];

    // @ts-expect-error
    const trending = featuredCourses.map((uuid) => allCourses.find((c) => c.uuid === uuid)).filter(Boolean);
    allCourses = allCourses.filter(
      // @ts-expect-error
      (course) => !featuredCourses.includes(course.uuid),
    );
    allCourses = [...trending];

    if (subject && subjectToCategory[subject]) {
      allCourses = allCourses.filter((course) => course.subjects?.includes(subjectToCategory[subject]));
    }

    const filteredCourses = allCourses.filter((course) => {
      if (!keywords || keywords.length < 2) return true;
      const courseTitle = course.title?.toLowerCase().replace(/\s+/g, "") || "";
      const searchKeywords = keywords.toLowerCase().replace(/\s+/g, "");
      const searchTerms = course.search_terms || [];

      return courseTitle.includes(searchKeywords) || searchTerms.some((term) => term.toLowerCase().replace(/\s+/g, "").includes(searchKeywords));
    });

    return filteredCourses;
  }, [keywords, subject]);

  const firstThreeCourses = results.slice(0, 3);
  const remainingCourses = results.slice(3);

  return (
    <div class='creative_courses_landing__grid'>
      {no_tabs === "true" ? null : <CoursesTabs setSelectedCourse={setSelectedCourse} theme={theme} />}
      <div>
        {firstThreeCourses.map((course) => (
          <Course key={course.uuid} {...course} theme={theme} />
        ))}
        {children}
        {remainingCourses.map((course) => (
          <Course key={course.uuid} {...course} theme={theme} />
        ))}
      </div>
    </div>
  );
}

const Course = ({ url, image, short_description, title, arlo_codes, theme }) => {
  let variants = useArloVariants(arlo_codes);
  return (
    <div class='creative_courses__course_landing'>
      <div class='creative_courses__header'>
        <div class='creative_courses__info'>
          <a href={url}>
            <h3 class='creative_courses__title'>{title}</h3>
          </a>
          <p class='creative_courses__description'>{short_description}</p>
        </div>
        {image && <div class='creative_courses__image' style={{ backgroundImage: `url("${image}")` }}></div>}
      </div>

      <div class='creative_courses__variants'>
        {variants.map((variant) => (
          <Variant {...variant} theme={theme} />
        ))}
      </div>
    </div>
  );
};

const Variant = ({ heading, startDate, type, enrolNow, theme }) => {
  return (
    <div class='creative_courses__variant_row'>
      <h3 class='creative_courses__variant_heading'>
        {type === "On Demand" ? "Start today" : formatDateWithTime(startDate)} <span class='creative_courses__variant_separator'>|</span> <span class='creative_courses__variant_type'> {type}</span>
      </h3>
      {enrolNow ? (
        <a href={enrolNow} target='_blank' class='creative_courses__enrol_button'>
          Enrol Now
        </a>
      ) : (
        <span class='creative_courses__enrol_soon'>Registration opens soon!</span>
      )}
    </div>
  );
};
