<% var block = options.block %>
<%- block._editable %>

<div class="creative_courses_landing">

    <%
    var featuredCourses = block.featured || [];
    // Ensure we have a valid array and safely serialize it
    const safeFeaturedCourses = Array.isArray(featuredCourses) ? featuredCourses : [];
    const blockId = 'creative-courses-landing-' + block._uid;
    %>

    <!-- Store featured courses data in a script tag to avoid HTML attribute length limits -->
    <script type="application/json" id="<%- blockId %>-data">
    <%- JSON.stringify(safeFeaturedCourses) %>
    </script>

    <blocks-creative-courses-landing
    theme= "<%= block?.theme?.color %>"
        data-block-id="<%- blockId %>"
        no_tabs="<%- block.no_tabs ? 'true' : 'false' %>">
				    <!-- Call-to-action block -->
						<div class="creative_courses_landing_action ">
							<%- plugins.blocks(block.content) %>
					</div>

    </blocks-creative-courses-landing>

    <!-- Back to top link -->
    <div class="creative_courses_landing_back container">
        <a href="#top" class="back-to-top">back to top</a>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var backToTopButton = document.querySelector('.back-to-top');
        if (backToTopButton) {
            backToTopButton.addEventListener('click', function(event) {
                event.preventDefault();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        }
    });
</script>
