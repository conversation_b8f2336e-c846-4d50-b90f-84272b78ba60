<% var block = options.block %>
<%
// Fetch courses data using the stories plugin
const coursesData = plugins.stories({
  component: 'Creative Courses Module',
  sort: 'desc',
  order_by: 'position',
  context: 'find-your-course-schema',
  where: course => !course.data.hide
});

// Load Arlo courses data
const arloCourses = plugins.readJSONFile('data/arloCourses.json') || [];

// Function to find the lowest price for a course based on Arlo template codes
const findLowestPrice = (arloTemplateCodes) => {
  if (!arloTemplateCodes || typeof arloTemplateCodes !== 'string') {
    return null;
  }

  let lowestPrice = null;
  let currency = 'EUR';

  // Split the template codes string and trim whitespace
  const templateCodesArray = arloTemplateCodes.split(',').map(code => code.trim());

  // Find all Arlo courses that match the template codes
  const matchingCourses = arloCourses.filter(arloCourse =>
    templateCodesArray.includes(arloCourse.TemplateCode)
  );



  // Extract prices from all matching courses
  matchingCourses.forEach(arloCourse => {
    if (arloCourse.AdvertisedOffers && Array.isArray(arloCourse.AdvertisedOffers)) {
      arloCourse.AdvertisedOffers.forEach(offer => {
        if (offer.OfferAmount && offer.OfferAmount.AmountTaxInclusive) {
          const price = offer.OfferAmount.AmountTaxInclusive;
          if (lowestPrice === null || price < lowestPrice) {
            lowestPrice = price;
            currency = offer.OfferAmount.CurrencyCode || 'EUR';
          }
        }
      });
    }
  });

  return lowestPrice ? { price: lowestPrice.toString(), currency } : null;
};

// Format courses for schema
const coursesSchema = (coursesData.stories || []).map(course => {
  const courseSchema = {
    "@type": "Course",
    "name": course.data.short_heading || course.title,
    "description": course.data.short_description || '',
    "url": course.url,
    "provider": {
      "@type": "CollegeOrUniversity",
      "name": "UCD Professional Academy",
      "url": "https://www.ucd.ie/professionalacademy/"
    }
  };

  // Add pricing information if available
  const priceInfo = findLowestPrice(course.data.arlo_template_codes_for_variants);
  if (priceInfo) {
    courseSchema.offers = {
      "@type": "Offer",
      "price": priceInfo.price,
      "priceCurrency": priceInfo.currency
    };
  }

  return courseSchema;
});

// Parse base schema from Storyblok
const baseSchemaFromSB = JSON.parse(block.json_schema || '{}');

// Combine schemas
const schema = {
  ...baseSchemaFromSB,
  hasPart: coursesSchema
}
%>

<script type="application/ld+json">
<%- JSON.stringify(schema, null, 2) %>
</script>
