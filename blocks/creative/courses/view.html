<% var block = options.block %>
<%- block._editable %>
<%
const featuredCourses = block.featured || [];
// Ensure we have a valid array and safely serialize it
const safeFeaturedCourses = Array.isArray(featuredCourses) ? featuredCourses : [];
const featuredCoursesJson = JSON.stringify(safeFeaturedCourses);
%>

<div class="creative_courses">
	<div class="container">
		<blocks-creative-courses featured="<%- featuredCoursesJson %>"></blocks-creative-courses>
	</div>
</div>