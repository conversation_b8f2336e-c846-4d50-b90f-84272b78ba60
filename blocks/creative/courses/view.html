<% var block = options.block %>
<%- block._editable %>
<%
const featuredCourses = block.featured || [];
// Ensure we have a valid array and safely serialize it
const safeFeaturedCourses = Array.isArray(featuredCourses) ? featuredCourses : [];
const blockId = 'creative-courses-' + block._uid;
%>

<!-- Store featured courses data in a script tag to avoid HTML attribute length limits -->
<script type="application/json" id="<%- blockId %>-data">
<%- JSON.stringify(safeFeaturedCourses) %>
</script>

<div class="creative_courses">
	<div class="container">
		<blocks-creative-courses data-block-id="<%- blockId %>"></blocks-creative-courses>
	</div>
</div>